version: '3.8'

services:
  # Main trading application
  exodus-app:
    build:
      context: .
      dockerfile: docker/simulation/Dockerfile
    container_name: exodus-trading
    environment:
      - EXODUS_ENV=development
      - DATABASE_URL=*********************************************/exodus_db
      - REDIS_URL=redis://redis:6379/0
      - PYTHONPATH=/app
    depends_on:
      - postgres
      - redis
      - timescaledb
    volumes:
      - ./logs:/app/logs
      - ./data:/app/data
      - ./results:/app/results
      - ./config:/app/config:ro
    ports:
      - "8000:8000"
    networks:
      - exodus-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "python", "scripts/maintenance/health_check.py"]
      interval: 30s
      timeout: 10s
      retries: 3
    profiles: ['dev']

  # PostgreSQL database
  postgres:
    image: postgres:15-alpine
    container_name: exodus-postgres
    environment:
      - POSTGRES_DB=exodus_db
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=exodus123
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=C --lc-ctype=C
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/database/init.sql:/docker-entrypoint-initdb.d/init.sql:ro
    ports:
      - "5432:5432"
    networks:
      - exodus-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d exodus_db"]
      interval: 10s
      timeout: 5s
      retries: 5

  # TimescaleDB for time-series data
  timescaledb:
    image: timescale/timescaledb:latest-pg15
    container_name: exodus-timescaledb
    environment:
      - POSTGRES_DB=exodus_timeseries
      - POSTGRES_USER=timescale
      - POSTGRES_PASSWORD=timescale123
    volumes:
      - timescale_data:/var/lib/postgresql/data
      - ./scripts/database/timescale_init.sql:/docker-entrypoint-initdb.d/init.sql:ro
    ports:
      - "5433:5432"
    networks:
      - exodus-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U timescale -d exodus_timeseries"]
      interval: 10s
      timeout: 5s
      retries: 5
    profiles: ['dev']

  # Redis for caching and message queuing
  redis:
    image: redis:7-alpine
    container_name: exodus-redis
    command: redis-server --appendonly yes --requirepass redis123
    volumes:
      - redis_data:/data
      - ./config/redis/redis.conf:/usr/local/etc/redis/redis.conf:ro
    ports:
      - "6379:6379"
    networks:
      - exodus-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5

  # Prometheus for metrics collection
  prometheus:
    image: prom/prometheus:latest
    container_name: exodus-prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    volumes:
      - ./monitoring/metrics/prometheus/config:/etc/prometheus:ro
      - prometheus_data:/prometheus
    ports:
      - "9090:9090"
    networks:
      - exodus-network
    restart: unless-stopped
    depends_on:
      - exodus-app

  # Grafana for dashboards
  grafana:
    image: grafana/grafana:latest
    container_name: exodus-grafana
    environment:
      - GF_SECURITY_ADMIN_USER=admin
      - GF_SECURITY_ADMIN_PASSWORD=grafana123
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/dashboards/grafana:/etc/grafana/provisioning:ro
    ports:
      - "3000:3000"
    networks:
      - exodus-network
    restart: unless-stopped
    depends_on:
      - prometheus

  # Jaeger for distributed tracing
  jaeger:
    image: jaegertracing/all-in-one:latest
    container_name: exodus-jaeger
    environment:
      - COLLECTOR_OTLP_ENABLED=true
    ports:
      - "16686:16686"  # Jaeger UI
      - "14268:14268"  # Jaeger collector HTTP
      - "14250:14250"  # Jaeger collector gRPC
      - "4317:4317"    # OTLP gRPC receiver
      - "4318:4318"    # OTLP HTTP receiver
    networks:
      - exodus-network
    restart: unless-stopped

  # Jupyter notebook server
  jupyter:
    build:
      context: .
      dockerfile: docker/simulation/Dockerfile
    container_name: exodus-jupyter
    command: jupyter lab --ip=0.0.0.0 --port=8888 --no-browser --allow-root --NotebookApp.token='' --NotebookApp.password=''
    environment:
      - EXODUS_ENV=development
      - DATABASE_URL=*********************************************/exodus_db
      - REDIS_URL=redis://redis:6379/0
    volumes:
      - ./notebooks:/app/notebooks
      - ./src:/app/src:ro
      - ./data:/app/data
      - ./results:/app/results
    ports:
      - "8888:8888"
    networks:
      - exodus-network
    restart: unless-stopped
    depends_on:
      - postgres
      - redis
    profiles: ['dev']

  # Nginx reverse proxy
  nginx:
    image: nginx:alpine
    container_name: exodus-nginx
    volumes:
      - ./docker/nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./docker/nginx/ssl:/etc/nginx/ssl:ro
    ports:
      - "80:80"
      - "443:443"
    networks:
      - exodus-network
    restart: unless-stopped
    depends_on:
      - exodus-app
      - grafana
      - jupyter

  # Elasticsearch for log aggregation
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0
    container_name: exodus-elasticsearch
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    ports:
      - "9200:9200"
    networks:
      - exodus-network
    restart: unless-stopped

  # Kibana for log visualization
  kibana:
    image: docker.elastic.co/kibana/kibana:8.11.0
    container_name: exodus-kibana
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
    ports:
      - "5601:5601"
    networks:
      - exodus-network
    restart: unless-stopped
    depends_on:
      - elasticsearch

  # Logstash for log processing
  logstash:
    image: docker.elastic.co/logstash/logstash:8.11.0
    container_name: exodus-logstash
    volumes:
      - ./monitoring/observability/logs/logstash.conf:/usr/share/logstash/pipeline/logstash.conf:ro
      - ./logs:/app/logs:ro
    networks:
      - exodus-network
    restart: unless-stopped
    depends_on:
      - elasticsearch

volumes:
  postgres_data:
    driver: local
  timescale_data:
    driver: local
  redis_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  elasticsearch_data:
    driver: local

networks:
  exodus-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
