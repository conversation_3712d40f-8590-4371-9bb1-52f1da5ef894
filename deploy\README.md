# Deployment Guide

## Overview

Infrastructure as Code (IaC) and deployment automation for the Exodus trading system across multiple cloud providers and environments.

## Structure

### 📁 environments/
Environment-specific configurations
- **development/**: Development environment setup
- **staging/**: Staging environment configuration
- **production/**: Production environment deployment

Each environment contains:
- `terraform/`: Infrastructure provisioning
- `ansible/`: Configuration management
- `kubernetes/`: Container orchestration

### 📁 infrastructure/
Cloud provider infrastructure definitions
- **aws/**: Amazon Web Services resources
- **gcp/**: Google Cloud Platform resources
- **azure/**: Microsoft Azure resources

### 📁 automation/
Deployment automation tools
- **ansible/**: Configuration management playbooks
- **terraform/**: Infrastructure modules and scripts

### 📁 scripts/
Deployment utility scripts
- `deploy.sh`: Main deployment script
- `rollback.sh`: Rollback procedures
- `scale.sh`: Scaling operations
- `health_check.sh`: Post-deployment validation

## Deployment Strategies

### Blue-Green Deployment
Zero-downtime deployment strategy:

```bash
# Deploy to green environment
./scripts/deploy.sh --env production --strategy blue-green --target green

# Validate green environment
./scripts/health_check.sh --env production-green

# Switch traffic to green
./scripts/switch_traffic.sh --from blue --to green

# Cleanup blue environment
./scripts/cleanup.sh --env production-blue
```

### Canary Deployment
Gradual rollout strategy:

```bash
# Deploy canary version (10% traffic)
./scripts/deploy.sh --env production --strategy canary --percentage 10

# Monitor metrics
./scripts/monitor_canary.sh --duration 30m

# Increase traffic gradually
./scripts/scale_canary.sh --percentage 50

# Complete rollout
./scripts/complete_canary.sh
```

### Rolling Deployment
Sequential instance updates:

```bash
# Rolling update
./scripts/deploy.sh --env production --strategy rolling --batch-size 2

# Monitor progress
kubectl rollout status deployment/trading-engine
```

## Infrastructure as Code

### Terraform Configuration

#### AWS Infrastructure
```hcl
# deploy/infrastructure/aws/main.tf
provider "aws" {
  region = var.aws_region
}

# VPC Configuration
module "vpc" {
  source = "terraform-aws-modules/vpc/aws"
  
  name = "exodus-vpc"
  cidr = "10.0.0.0/16"
  
  azs             = ["${var.aws_region}a", "${var.aws_region}b"]
  private_subnets = ["********/24", "********/24"]
  public_subnets  = ["**********/24", "**********/24"]
  
  enable_nat_gateway = true
  enable_vpn_gateway = true
}

# EKS Cluster
module "eks" {
  source = "terraform-aws-modules/eks/aws"
  
  cluster_name    = "exodus-cluster"
  cluster_version = "1.27"
  
  vpc_id     = module.vpc.vpc_id
  subnet_ids = module.vpc.private_subnets
  
  node_groups = {
    trading = {
      desired_capacity = 3
      max_capacity     = 10
      min_capacity     = 1
      
      instance_types = ["m5.large"]
      
      labels = {
        role = "trading"
      }
    }
  }
}

# RDS Database
resource "aws_db_instance" "exodus_db" {
  identifier = "exodus-db"
  
  engine         = "postgres"
  engine_version = "15.3"
  instance_class = "db.t3.medium"
  
  allocated_storage     = 100
  max_allocated_storage = 1000
  storage_encrypted     = true
  
  db_name  = "exodus"
  username = "exodus_admin"
  password = var.db_password
  
  vpc_security_group_ids = [aws_security_group.rds.id]
  db_subnet_group_name   = aws_db_subnet_group.exodus.name
  
  backup_retention_period = 7
  backup_window          = "03:00-04:00"
  maintenance_window     = "sun:04:00-sun:05:00"
  
  skip_final_snapshot = false
  final_snapshot_identifier = "exodus-db-final-snapshot"
}
```

#### GCP Infrastructure
```hcl
# deploy/infrastructure/gcp/main.tf
provider "google" {
  project = var.project_id
  region  = var.region
}

# GKE Cluster
resource "google_container_cluster" "exodus_cluster" {
  name     = "exodus-cluster"
  location = var.region
  
  remove_default_node_pool = true
  initial_node_count       = 1
  
  network    = google_compute_network.vpc.name
  subnetwork = google_compute_subnetwork.subnet.name
  
  workload_identity_config {
    workload_pool = "${var.project_id}.svc.id.goog"
  }
}

# Node Pool
resource "google_container_node_pool" "trading_nodes" {
  name       = "trading-node-pool"
  location   = var.region
  cluster    = google_container_cluster.exodus_cluster.name
  node_count = 3
  
  node_config {
    preemptible  = false
    machine_type = "e2-standard-4"
    
    oauth_scopes = [
      "https://www.googleapis.com/auth/cloud-platform"
    ]
    
    labels = {
      role = "trading"
    }
  }
  
  autoscaling {
    min_node_count = 1
    max_node_count = 10
  }
}

# Cloud SQL
resource "google_sql_database_instance" "exodus_db" {
  name             = "exodus-db"
  database_version = "POSTGRES_15"
  region           = var.region
  
  settings {
    tier = "db-standard-2"
    
    backup_configuration {
      enabled    = true
      start_time = "03:00"
    }
    
    ip_configuration {
      ipv4_enabled    = false
      private_network = google_compute_network.vpc.id
    }
  }
}
```

### Ansible Configuration

#### Playbook Structure
```yaml
# deploy/automation/ansible/playbooks/deploy.yml
---
- name: Deploy Exodus Trading System
  hosts: all
  become: yes
  vars:
    app_name: exodus
    app_version: "{{ version | default('latest') }}"
    environment: "{{ env | default('development') }}"
  
  roles:
    - common
    - docker
    - kubernetes
    - monitoring
    - security
  
  tasks:
    - name: Deploy application
      kubernetes.core.k8s:
        state: present
        definition:
          apiVersion: apps/v1
          kind: Deployment
          metadata:
            name: "{{ app_name }}-{{ environment }}"
            namespace: "{{ app_name }}"
          spec:
            replicas: "{{ replicas | default(2) }}"
            selector:
              matchLabels:
                app: "{{ app_name }}"
                environment: "{{ environment }}"
            template:
              metadata:
                labels:
                  app: "{{ app_name }}"
                  environment: "{{ environment }}"
              spec:
                containers:
                - name: "{{ app_name }}"
                  image: "{{ app_name }}:{{ app_version }}"
                  env:
                  - name: EXODUS_ENV
                    value: "{{ environment }}"
```

## Environment Management

### Development Environment
```bash
# Initialize development environment
cd deploy/environments/development/terraform
terraform init
terraform plan -var-file="dev.tfvars"
terraform apply

# Configure with Ansible
cd ../../automation/ansible
ansible-playbook -i inventory/dev playbooks/deploy.yml -e env=development
```

### Staging Environment
```bash
# Deploy to staging
./scripts/deploy.sh --env staging --version v1.2.3

# Run integration tests
./scripts/run_tests.sh --env staging --type integration

# Validate deployment
./scripts/health_check.sh --env staging
```

### Production Environment
```bash
# Production deployment (requires approval)
./scripts/deploy.sh --env production --version v1.2.3 --strategy blue-green

# Monitor deployment
./scripts/monitor_deployment.sh --env production --duration 1h

# Validate and switch traffic
./scripts/validate_and_switch.sh --env production
```

## Security and Compliance

### Secret Management
```bash
# Encrypt secrets with Ansible Vault
ansible-vault encrypt deploy/environments/production/secrets.yml

# Deploy with secrets
ansible-playbook -i inventory/prod playbooks/deploy.yml --ask-vault-pass
```

### Network Security
- VPC with private subnets
- Security groups and NACLs
- WAF for web applications
- VPN for administrative access

### Compliance
- Encryption at rest and in transit
- Audit logging
- Access controls
- Regular security scans

## Monitoring and Alerting

### Deployment Monitoring
```bash
# Monitor deployment progress
kubectl rollout status deployment/trading-engine

# Check application health
curl -f http://trading-engine:8080/health

# View application logs
kubectl logs -f deployment/trading-engine
```

### Post-Deployment Validation
```bash
# Run health checks
./scripts/health_check.sh --comprehensive

# Validate trading functionality
./scripts/validate_trading.sh --paper-trading

# Performance testing
./scripts/performance_test.sh --duration 10m
```

## Disaster Recovery

### Backup Procedures
```bash
# Database backup
./scripts/backup_database.sh --env production

# Configuration backup
./scripts/backup_config.sh --env production

# Application state backup
./scripts/backup_state.sh --env production
```

### Recovery Procedures
```bash
# Restore from backup
./scripts/restore.sh --env production --backup-id 20250101-120000

# Validate recovery
./scripts/validate_recovery.sh --env production
```

## Troubleshooting

### Common Issues
```bash
# Check deployment status
kubectl get deployments -n exodus

# View pod logs
kubectl logs -l app=trading-engine -n exodus

# Debug networking
kubectl exec -it <pod-name> -- nslookup database-service

# Check resource usage
kubectl top pods -n exodus
```

### Rollback Procedures
```bash
# Quick rollback
kubectl rollout undo deployment/trading-engine

# Rollback to specific version
./scripts/rollback.sh --env production --version v1.2.2

# Emergency rollback
./scripts/emergency_rollback.sh --env production
```
