# Exodus v2025 - Advanced Quantitative Trading System

## Overview

Exodus v2025 is a comprehensive quantitative trading system designed for both simulation and live trading environments. The system provides a unified architecture that supports multiple brokers, advanced risk management, and enterprise-grade monitoring.

## Key Features

- **Unified Architecture**: Single codebase for simulation and live trading
- **Multi-Broker Support**: Interactive Brokers, Alpaca, Binance, TD Ameritrade
- **Advanced Risk Management**: Real-time monitoring and circuit breakers
- **Enterprise Security**: HashiCorp Vault integration and encrypted configurations
- **Comprehensive Monitoring**: Prometheus, Grafana, and OpenTelemetry integration
- **Model Versioning**: MLflow and DVC for experiment tracking
- **Containerized Deployment**: Docker and Kubernetes support

## Quick Start

### Prerequisites

- Python 3.10+
- Poetry for dependency management
- Docker (optional)

### Installation

```bash
# Clone the repository
git clone <repository-url>
cd Exodus_v2025

# Install dependencies
poetry install

# Install simulation dependencies
poetry install --with simulation

# Install live trading dependencies
poetry install --with live
```

### Running Simulations

```bash
# Run a basic simulation
python scripts/simulation/run_simulation.py

# Run with custom configuration
python scripts/simulation/run_simulation.py --config config/simulation/broker_sim.yaml
```

### Live Trading

```bash
# Start live trading (requires proper configuration)
python scripts/live/start_live_trading.py

# Emergency stop
python scripts/live/emergency_stop.py
```

## Architecture

The system is organized into several key modules:

- **src/**: Core trading engine and unified execution
- **simulation/**: Simulation-specific components
- **live/**: Live trading components
- **config/**: Configuration management
- **monitoring/**: Observability and alerting
- **tests/**: Comprehensive test suite

## Documentation

- [Architecture Guide](docs/architecture/system_design.md)
- [Configuration Guide](docs/user_guides/configuration_guide.md)
- [Strategy Development](docs/user_guides/strategy_development.md)
- [API Reference](docs/api_reference/)

## Contributing

Please read [CONTRIBUTING.md](docs/development/contributing.md) for details on our code of conduct and the process for submitting pull requests.

## License

This project is licensed under the MIT License - see the LICENSE file for details.
