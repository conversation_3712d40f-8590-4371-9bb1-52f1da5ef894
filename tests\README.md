# Testing Guide

## Overview

Comprehensive testing framework for the Exodus trading system, covering unit tests, integration tests, performance tests, and specialized trading system tests.

## Test Structure

### 📁 simulation/
Tests for simulation components
- `test_broker_simulator.py`: Broker simulation testing
- `test_market_data.py`: Market data simulation tests
- `test_validation.py`: Simulation validation tests

### 📁 live/
Tests for live trading components
- `test_broker_connections.py`: Real broker connection tests
- `test_data_feeds.py`: Live data feed tests
- `test_execution.py`: Live execution tests

### 📁 src/
Tests for core source code
- **execution/**: Trading engine and execution tests
- **risk_management/**: Risk management tests
- **portfolio/**: Portfolio management tests
- **data/**: Data handling tests

### 📁 integration/
End-to-end integration tests
- `test_end_to_end.py`: Complete system integration tests
- `test_broker_integration.py`: Broker integration tests

### 📁 performance/
Performance and load tests
- `test_latency.py`: Latency measurement tests
- `test_throughput.py`: Throughput performance tests

### 📁 feature_flags/
Feature flag system tests
- `test_toggle_strategies.py`: Strategy toggle tests
- `test_ab_testing.py`: A/B testing framework tests

### 📁 disaster_recovery/
Disaster recovery and failsafe tests
- `test_kill_switch.py`: Emergency stop tests
- `test_emergency_exit.py`: Emergency exit tests
- `test_failsafe_scenarios.py`: Failsafe scenario tests

### 📁 distributed/
Distributed computing tests
- `test_dask_backtesting.py`: Dask-based backtesting tests
- `test_ray_optimization.py`: Ray optimization tests

## Test Categories

### Unit Tests
- Fast, isolated tests for individual components
- Mock external dependencies
- High code coverage requirements

### Integration Tests
- Test component interactions
- Use test databases and mock brokers
- Validate data flow between modules

### Performance Tests
- Latency and throughput measurements
- Load testing under various conditions
- Memory and CPU usage profiling

### End-to-End Tests
- Complete workflow testing
- Simulation and live trading scenarios
- User acceptance testing

## Running Tests

### All Tests
```bash
poetry run pytest
```

### Specific Test Categories
```bash
# Unit tests only
poetry run pytest -m unit

# Integration tests
poetry run pytest -m integration

# Performance tests
poetry run pytest -m performance

# Simulation tests
poetry run pytest tests/simulation/

# Live trading tests (requires configuration)
poetry run pytest tests/live/
```

### Coverage Reports
```bash
poetry run pytest --cov=src --cov-report=html
```

## Test Configuration

### conftest.py
Global test configuration and fixtures:
- Database setup and teardown
- Mock broker configurations
- Test data generation
- Common test utilities

### Fixtures
- `mock_broker`: Mock broker for testing
- `test_portfolio`: Test portfolio with sample data
- `market_data`: Sample market data for testing
- `risk_config`: Test risk management configuration

## Continuous Integration

Tests are automatically run on:
- Every pull request
- Main branch commits
- Scheduled daily runs
- Release candidates

### Test Requirements
- All tests must pass
- Code coverage > 90%
- No security vulnerabilities
- Performance benchmarks met

## Best Practices

1. **Test Isolation**: Each test should be independent
2. **Mock External Services**: Don't rely on external APIs in tests
3. **Descriptive Names**: Test names should describe what they test
4. **Arrange-Act-Assert**: Follow AAA pattern
5. **Edge Cases**: Test boundary conditions and error cases
