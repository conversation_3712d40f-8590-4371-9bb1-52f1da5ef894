-- Inicialización de TimescaleDB para Exodus v2025
-- Base de datos especializada en series temporales para datos crypto

-- Crear extensión TimescaleDB
CREATE EXTENSION IF NOT EXISTS timescaledb;
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- <PERSON><PERSON><PERSON> esquemas
CREATE SCHEMA IF NOT EXISTS crypto_data;
CREATE SCHEMA IF NOT EXISTS analytics;
CREATE SCHEMA IF NOT EXISTS predictions;

-- Tabla principal para datos OHLCV de criptomonedas
CREATE TABLE IF NOT EXISTS crypto_data.ohlcv (
    time TIMESTAMPTZ NOT NULL,
    symbol VARCHAR(50) NOT NULL,
    exchange VARCHAR(50) NOT NULL,
    timeframe VARCHAR(10) NOT NULL, -- '1m', '5m', '15m', '1h', '4h', '1d'
    open DECIMAL(20, 8) NOT NULL,
    high DECIMAL(20, 8) NOT NULL,
    low DECIMAL(20, 8) NOT NULL,
    close DECIMAL(20, 8) NOT NULL,
    volume DECIMAL(20, 8) NOT NULL,
    quote_volume DECIMAL(20, 8),
    trades_count INTEGER,
    taker_buy_volume DECIMAL(20, 8),
    taker_buy_quote_volume DECIMAL(20, 8),
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Convertir a hypertable (característica principal de TimescaleDB)
SELECT create_hypertable('crypto_data.ohlcv', 'time', 
    chunk_time_interval => INTERVAL '1 day',
    if_not_exists => TRUE
);

-- Crear índice compuesto para consultas eficientes
CREATE INDEX IF NOT EXISTS idx_ohlcv_symbol_time ON crypto_data.ohlcv (symbol, time DESC);
CREATE INDEX IF NOT EXISTS idx_ohlcv_exchange_symbol ON crypto_data.ohlcv (exchange, symbol);
CREATE INDEX IF NOT EXISTS idx_ohlcv_timeframe ON crypto_data.ohlcv (timeframe);

-- Tabla para datos de orderbook
CREATE TABLE IF NOT EXISTS crypto_data.orderbook_snapshots (
    time TIMESTAMPTZ NOT NULL,
    symbol VARCHAR(50) NOT NULL,
    exchange VARCHAR(50) NOT NULL,
    bids JSONB NOT NULL, -- Array de [price, quantity]
    asks JSONB NOT NULL, -- Array de [price, quantity]
    best_bid DECIMAL(20, 8),
    best_ask DECIMAL(20, 8),
    spread DECIMAL(20, 8),
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Convertir orderbook a hypertable
SELECT create_hypertable('crypto_data.orderbook_snapshots', 'time',
    chunk_time_interval => INTERVAL '1 hour',
    if_not_exists => TRUE
);

-- Tabla para trades individuales
CREATE TABLE IF NOT EXISTS crypto_data.trades (
    time TIMESTAMPTZ NOT NULL,
    symbol VARCHAR(50) NOT NULL,
    exchange VARCHAR(50) NOT NULL,
    trade_id VARCHAR(100),
    price DECIMAL(20, 8) NOT NULL,
    quantity DECIMAL(20, 8) NOT NULL,
    side VARCHAR(10), -- 'buy' or 'sell'
    is_maker BOOLEAN,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Convertir trades a hypertable
SELECT create_hypertable('crypto_data.trades', 'time',
    chunk_time_interval => INTERVAL '1 hour',
    if_not_exists => TRUE
);

-- Tabla para indicadores técnicos calculados
CREATE TABLE IF NOT EXISTS analytics.technical_indicators (
    time TIMESTAMPTZ NOT NULL,
    symbol VARCHAR(50) NOT NULL,
    timeframe VARCHAR(10) NOT NULL,
    indicator_name VARCHAR(100) NOT NULL,
    indicator_value DECIMAL(20, 8),
    indicator_data JSONB, -- Para indicadores complejos como Bollinger Bands
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Convertir indicadores a hypertable
SELECT create_hypertable('analytics.technical_indicators', 'time',
    chunk_time_interval => INTERVAL '1 day',
    if_not_exists => TRUE
);

-- Índices para indicadores técnicos
CREATE INDEX IF NOT EXISTS idx_indicators_symbol_name_time ON analytics.technical_indicators (symbol, indicator_name, time DESC);

-- Tabla para predicciones de ML
CREATE TABLE IF NOT EXISTS predictions.price_predictions (
    time TIMESTAMPTZ NOT NULL,
    symbol VARCHAR(50) NOT NULL,
    model_name VARCHAR(100) NOT NULL,
    model_version VARCHAR(50) NOT NULL,
    prediction_horizon VARCHAR(20) NOT NULL, -- '1h', '4h', '1d', '7d'
    predicted_price DECIMAL(20, 8) NOT NULL,
    confidence_score DECIMAL(5, 4), -- 0.0 to 1.0
    actual_price DECIMAL(20, 8), -- Se llena después para evaluar el modelo
    prediction_error DECIMAL(10, 6), -- Se calcula después
    features JSONB, -- Features usadas para la predicción
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Convertir predicciones a hypertable
SELECT create_hypertable('predictions.price_predictions', 'time',
    chunk_time_interval => INTERVAL '1 day',
    if_not_exists => TRUE
);

-- Índices para predicciones
CREATE INDEX IF NOT EXISTS idx_predictions_symbol_model_time ON predictions.price_predictions (symbol, model_name, time DESC);
CREATE INDEX IF NOT EXISTS idx_predictions_horizon ON predictions.price_predictions (prediction_horizon);

-- Tabla para métricas de rendimiento de modelos
CREATE TABLE IF NOT EXISTS predictions.model_performance (
    time TIMESTAMPTZ NOT NULL,
    model_name VARCHAR(100) NOT NULL,
    model_version VARCHAR(50) NOT NULL,
    symbol VARCHAR(50) NOT NULL,
    prediction_horizon VARCHAR(20) NOT NULL,
    mae DECIMAL(10, 6), -- Mean Absolute Error
    mse DECIMAL(10, 6), -- Mean Squared Error
    rmse DECIMAL(10, 6), -- Root Mean Squared Error
    mape DECIMAL(10, 6), -- Mean Absolute Percentage Error
    accuracy_score DECIMAL(5, 4), -- Para clasificación direccional
    predictions_count INTEGER,
    evaluation_period INTERVAL,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Convertir métricas a hypertable
SELECT create_hypertable('predictions.model_performance', 'time',
    chunk_time_interval => INTERVAL '1 day',
    if_not_exists => TRUE
);

-- Políticas de retención de datos (compression y drop)
-- Comprimir datos OHLCV después de 7 días
SELECT add_compression_policy('crypto_data.ohlcv', INTERVAL '7 days', if_not_exists => TRUE);

-- Comprimir orderbook después de 1 día (datos muy voluminosos)
SELECT add_compression_policy('crypto_data.orderbook_snapshots', INTERVAL '1 day', if_not_exists => TRUE);

-- Comprimir trades después de 3 días
SELECT add_compression_policy('crypto_data.trades', INTERVAL '3 days', if_not_exists => TRUE);

-- Eliminar datos muy antiguos para ahorrar espacio
-- Mantener OHLCV por 2 años
SELECT add_retention_policy('crypto_data.ohlcv', INTERVAL '2 years', if_not_exists => TRUE);

-- Mantener orderbook por 30 días (muy voluminoso)
SELECT add_retention_policy('crypto_data.orderbook_snapshots', INTERVAL '30 days', if_not_exists => TRUE);

-- Mantener trades por 6 meses
SELECT add_retention_policy('crypto_data.trades', INTERVAL '6 months', if_not_exists => TRUE);

-- Mantener indicadores por 1 año
SELECT add_retention_policy('analytics.technical_indicators', INTERVAL '1 year', if_not_exists => TRUE);

-- Mantener predicciones por 1 año
SELECT add_retention_policy('predictions.price_predictions', INTERVAL '1 year', if_not_exists => TRUE);

-- Vistas materializadas para consultas frecuentes
-- Vista para últimos precios por símbolo
CREATE MATERIALIZED VIEW IF NOT EXISTS crypto_data.latest_prices AS
SELECT DISTINCT ON (symbol, exchange, timeframe)
    symbol,
    exchange,
    timeframe,
    time,
    close as price,
    volume
FROM crypto_data.ohlcv
WHERE timeframe = '1m'
ORDER BY symbol, exchange, timeframe, time DESC;

-- Índice para la vista materializada
CREATE UNIQUE INDEX IF NOT EXISTS idx_latest_prices_symbol_exchange ON crypto_data.latest_prices (symbol, exchange, timeframe);

-- Refrescar la vista cada minuto (se puede configurar con un job)
-- SELECT add_continuous_aggregate_policy('latest_prices', start_offset => INTERVAL '1 hour', end_offset => INTERVAL '1 minute', schedule_interval => INTERVAL '1 minute');

-- Funciones útiles para análisis
-- Función para calcular retornos
CREATE OR REPLACE FUNCTION crypto_data.calculate_returns(
    p_symbol VARCHAR(50),
    p_timeframe VARCHAR(10),
    p_periods INTEGER DEFAULT 1
)
RETURNS TABLE (
    time TIMESTAMPTZ,
    price DECIMAL(20, 8),
    return_pct DECIMAL(10, 6)
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        o.time,
        o.close,
        (o.close - LAG(o.close, p_periods) OVER (ORDER BY o.time)) / LAG(o.close, p_periods) OVER (ORDER BY o.time) * 100 as return_pct
    FROM crypto_data.ohlcv o
    WHERE o.symbol = p_symbol AND o.timeframe = p_timeframe
    ORDER BY o.time;
END;
$$ LANGUAGE plpgsql;

-- Función para calcular volatilidad
CREATE OR REPLACE FUNCTION crypto_data.calculate_volatility(
    p_symbol VARCHAR(50),
    p_timeframe VARCHAR(10),
    p_window INTEGER DEFAULT 20
)
RETURNS TABLE (
    time TIMESTAMPTZ,
    volatility DECIMAL(10, 6)
) AS $$
BEGIN
    RETURN QUERY
    WITH returns AS (
        SELECT * FROM crypto_data.calculate_returns(p_symbol, p_timeframe, 1)
    )
    SELECT 
        r.time,
        STDDEV(r.return_pct) OVER (ORDER BY r.time ROWS BETWEEN p_window-1 PRECEDING AND CURRENT ROW) as volatility
    FROM returns r
    WHERE r.return_pct IS NOT NULL
    ORDER BY r.time;
END;
$$ LANGUAGE plpgsql;

COMMIT;
