# Configuration Guide

## Overview

The configuration system provides environment-specific settings, security management, and feature flag controls for the Exodus trading system.

## Structure

### 📁 environments/
Environment-specific configurations
- `development.yaml`: Development environment settings
- `staging.yaml`: Staging environment settings  
- `production.yaml`: Production environment settings

### 📁 simulation/
Simulation-specific configurations
- `broker_sim.yaml`: Broker simulation parameters
- `market_sim.yaml`: Market simulation settings
- `latency_params.yaml`: Latency modeling configuration
- `slippage_params.yaml`: Slippage modeling configuration

### 📁 live/
Live trading configurations
- **broker_configs/**: Individual broker configurations
  - `ib_config.yaml`: Interactive Brokers settings
  - `alpaca_config.yaml`: Alpaca configuration
  - `binance_config.yaml`: Binance settings
  - `tda_config.yaml`: TD Ameritrade configuration
- `risk_limits.yaml`: Risk management limits
- `execution_params.yaml`: Execution parameters
- `monitoring_config.yaml`: Monitoring settings

### 📁 trading/
Trading strategy configurations
- `strategies.yaml`: Strategy parameters and settings
- `instruments.yaml`: Tradeable instruments configuration
- `timeframes.yaml`: Trading timeframe settings
- `position_sizing.yaml`: Position sizing rules

### 📁 data/
Data source configurations
- `data_sources.yaml`: Data provider settings
- `market_hours.yaml`: Market session definitions
- `data_validation.yaml`: Data quality rules

### 📁 security/
Security and encryption settings
- `vault_config.yaml`: HashiCorp Vault configuration
- `permissions.yaml`: Access control settings
- `encryption.yaml`: Encryption parameters

### 📁 feature_flags/
Feature flag configurations
- `strategies.yaml`: Strategy feature flags
- `modules.yaml`: Module feature flags
- `ab_tests.yaml`: A/B testing configurations

### 📁 sla_slo/
Service level configurations
- `service_levels.yaml`: SLA/SLO definitions
- `alert_thresholds.yaml`: Alert threshold settings

## Configuration Loading

The system uses a hierarchical configuration loading system:

1. **Base Configuration**: Default settings
2. **Environment Override**: Environment-specific overrides
3. **Local Override**: Local development overrides (not committed)
4. **Runtime Override**: Command-line and environment variable overrides

## Security

### Sensitive Data
- API keys and secrets are stored in HashiCorp Vault
- Local development uses encrypted configuration files
- Production uses cloud secret management services

### Access Control
- Role-based access to configuration sections
- Audit logging for configuration changes
- Encrypted storage for sensitive parameters

## Usage

```python
from src.utils.config import ConfigManager

# Load configuration
config = ConfigManager.load(
    environment="production",
    config_path="config/"
)

# Access configuration
broker_config = config.live.brokers.interactive_brokers
risk_limits = config.live.risk_limits
```

## Environment Variables

Key environment variables:
- `EXODUS_ENV`: Environment name (development/staging/production)
- `EXODUS_CONFIG_PATH`: Configuration directory path
- `VAULT_ADDR`: HashiCorp Vault address
- `VAULT_TOKEN`: Vault authentication token
