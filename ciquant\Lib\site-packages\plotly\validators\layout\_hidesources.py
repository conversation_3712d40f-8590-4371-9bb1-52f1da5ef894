import _plotly_utils.basevalidators


class HidesourcesValidator(_plotly_utils.basevalidators.BooleanValidator):
    def __init__(self, plotly_name="hidesources", parent_name="layout", **kwargs):
        super(HidesourcesValidator, self).__init__(
            plotly_name=plotly_name,
            parent_name=parent_name,
            edit_type=kwargs.pop("edit_type", "plot"),
            **kwargs,
        )
