
# This file was generated by 'versioneer.py' (0.21) from
# revision-control system data, or from the parent directory name of an
# unpacked source archive. Distribution tarballs contain a pre-generated copy
# of this file.

import json

version_json = '''
{
 "date": "2024-09-12T10:41:22-0400",
 "dirty": false,
 "error": null,
 "full-revisionid": "5d79b80c53f779274170743539a481c524e04c8f",
 "version": "5.24.1"
}
'''  # END VERSION_JSON


def get_versions():
    return json.loads(version_json)
