# Jupyter Notebooks

## Overview

Interactive Jupyter notebooks for learning, experimentation, and analysis with the Exodus trading system.

## Structure

### 📁 examples/
Beginner-friendly examples and tutorials
- `01_hello_world_backtest.ipynb`: Your first backtest
- `02_hello_world_live_trading.ipynb`: Paper trading setup
- `03_data_exploration.ipynb`: Market data analysis
- `04_strategy_development.ipynb`: Building strategies
- `05_risk_analysis.ipynb`: Risk management basics

### 📁 tutorials/
Comprehensive learning materials
- `getting_started.ipynb`: Complete beginner's guide
- `advanced_strategies.ipynb`: Complex strategy development
- `portfolio_optimization.ipynb`: Portfolio theory application
- `machine_learning_integration.ipynb`: ML in trading

### 📁 research/
Research and analysis notebooks
- `market_analysis.ipynb`: Market regime analysis
- `feature_engineering.ipynb`: Creating trading features
- `model_evaluation.ipynb`: Model performance analysis
- `alpha_research.ipynb`: Alpha factor research

## Getting Started

### Setup Jupyter Environment
```bash
# Install Jupyter with poetry
poetry add jupyter jupyterlab

# Start Jupyter Lab
poetry run jupyter lab

# Or start classic Jupyter
poetry run jupyter notebook
```

### Required Extensions
```bash
# Install useful extensions
poetry run jupyter labextension install @jupyter-widgets/jupyterlab-manager
poetry run jupyter labextension install plotlywidget
```

## Notebook Guidelines

### Code Structure
```python
# Standard imports at the top
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from src.execution.engine.trading_engine import TradingEngine

# Configuration
%matplotlib inline
pd.set_option('display.max_columns', None)
```

### Data Loading
```python
# Load sample data
from src.data.providers.yahoo_provider import YahooProvider

provider = YahooProvider()
data = provider.get_historical_data("AAPL", "2023-01-01", "2023-12-31")
```

### Visualization
```python
# Standard plotting setup
plt.style.use('seaborn-v0_8')
fig, ax = plt.subplots(figsize=(12, 6))

# Plot price data
data['Close'].plot(ax=ax, title='AAPL Price')
plt.show()
```

## Example Workflows

### 1. Basic Backtesting
```python
# Load strategy
from src.strategies.momentum_strategy import MomentumStrategy

# Initialize strategy
strategy = MomentumStrategy(
    lookback_period=20,
    threshold=0.02
)

# Run backtest
from simulation.environment.sim_engine import SimulationEngine

engine = SimulationEngine(
    strategy=strategy,
    initial_cash=100000,
    start_date="2023-01-01",
    end_date="2023-12-31"
)

results = engine.run()
```

### 2. Performance Analysis
```python
# Calculate performance metrics
from src.portfolio.performance_tracker import PerformanceTracker

tracker = PerformanceTracker(results)
metrics = tracker.calculate_metrics()

print(f"Total Return: {metrics['total_return']:.2%}")
print(f"Sharpe Ratio: {metrics['sharpe_ratio']:.2f}")
print(f"Max Drawdown: {metrics['max_drawdown']:.2%}")
```

### 3. Risk Analysis
```python
# Risk analysis
from src.risk_management.risk_calculator import RiskCalculator

risk_calc = RiskCalculator(results)
var_95 = risk_calc.calculate_var(confidence=0.95)
expected_shortfall = risk_calc.calculate_expected_shortfall()

print(f"VaR (95%): {var_95:.2%}")
print(f"Expected Shortfall: {expected_shortfall:.2%}")
```

## Best Practices

### Notebook Organization
1. **Clear Title**: Descriptive notebook title
2. **Introduction**: Explain the purpose and goals
3. **Imports**: All imports in the first cell
4. **Configuration**: Set up plotting and display options
5. **Sections**: Use markdown headers to organize
6. **Conclusions**: Summarize findings and next steps

### Code Quality
- Use meaningful variable names
- Add comments for complex logic
- Keep cells focused and concise
- Use functions for repeated code
- Include error handling

### Documentation
- Markdown cells for explanations
- Inline comments for code
- Document assumptions and limitations
- Include references and sources

### Version Control
- Clear commit messages
- Remove output before committing
- Use .gitignore for large files
- Tag important versions

## Interactive Features

### Widgets
```python
import ipywidgets as widgets
from IPython.display import display

# Interactive parameter selection
lookback_slider = widgets.IntSlider(
    value=20,
    min=5,
    max=100,
    description='Lookback:'
)

def update_strategy(lookback):
    # Update strategy parameters
    strategy.lookback_period = lookback
    # Rerun analysis
    
widgets.interact(update_strategy, lookback=lookback_slider)
```

### Plotly Integration
```python
import plotly.graph_objects as go
from plotly.subplots import make_subplots

# Interactive price chart
fig = go.Figure()
fig.add_trace(go.Scatter(
    x=data.index,
    y=data['Close'],
    mode='lines',
    name='Price'
))

fig.update_layout(
    title='Interactive Price Chart',
    xaxis_title='Date',
    yaxis_title='Price'
)

fig.show()
```

## Sharing and Collaboration

### Export Options
```bash
# Export to HTML
jupyter nbconvert --to html notebook.ipynb

# Export to PDF
jupyter nbconvert --to pdf notebook.ipynb

# Export to Python script
jupyter nbconvert --to script notebook.ipynb
```

### Collaboration
- Use clear cell outputs for sharing
- Include data sources and requirements
- Document environment setup
- Provide reproducible examples

## Troubleshooting

### Common Issues
```python
# Kernel restart
# Restart kernel if imports fail

# Memory issues
import gc
gc.collect()  # Free memory

# Plot not showing
%matplotlib inline
plt.show()
```

### Performance Tips
- Use vectorized operations
- Limit data size for exploration
- Clear variables when done
- Use efficient data types
