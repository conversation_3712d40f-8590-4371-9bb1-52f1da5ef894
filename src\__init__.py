"""
Exodus v2025 - Core Trading System

This package contains the core trading system components that provide
a unified interface for both simulation and live trading environments.
"""

__version__ = "2025.1.0"
__author__ = "Exodus Trading Team"

from .execution.engine.trading_engine import TradingEngine
from .portfolio.portfolio_manager import PortfolioManager
from .risk_management.risk_monitor import RiskMonitor
from .data.feeds.feed_aggregator import FeedAggregator

__all__ = [
    "TradingEngine",
    "PortfolioManager", 
    "RiskMonitor",
    "FeedAggregator",
]
