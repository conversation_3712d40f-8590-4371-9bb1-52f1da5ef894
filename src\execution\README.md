# Execution Module

## Overview

The execution module is the core of the trading system, providing a unified interface for order execution across different environments (simulation and live trading) and brokers.

## Architecture

### Interfaces
- `broker_interface.py`: Abstract broker interface
- `data_interface.py`: Abstract data feed interface  
- `order_interface.py`: Abstract order management interface
- `execution_interface.py`: Abstract execution interface

### Adapters
- `simulation_adapter.py`: Adapter for simulation environment
- `ib_adapter.py`: Interactive Brokers adapter
- `alpaca_adapter.py`: Alpaca adapter
- `binance_adapter.py`: Binance adapter
- `tda_adapter.py`: TD Ameritrade adapter

### Engine
- `trading_engine.py`: Main trading engine
- `signal_processor.py`: Signal processing and validation
- `execution_engine.py`: Order execution logic
- `mode_controller.py`: Environment mode switching

### Order Management
- `order_router.py`: Order routing logic
- `order_tracker.py`: Order state tracking
- `fill_manager.py`: Fill processing and management
- `order_validator.py`: Order validation and risk checks

## Usage

```python
from src.execution.engine.trading_engine import TradingEngine
from src.execution.adapters.simulation_adapter import SimulationAdapter

# Initialize with simulation adapter
adapter = SimulationAdapter(config)
engine = TradingEngine(adapter=adapter)

# Start trading
engine.start()
```
