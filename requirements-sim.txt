# Exodus Trading System - Dependencies
# Core dependencies
numpy>=1.24.0
pandas>=2.0.0
scipy>=1.10.0

# Data processing and analysis
yfinance>=0.2.0
ccxt>=4.0.0
alpha-vantage>=2.3.0

# Technical indicators
#ta-lib>=0.4.0
vectorbt>=0.25.0

# Machine Learning
scikit-learn>=1.3.0
xgboost>=1.7.0
tensorflow>=2.13.0
keras>=2.13.0

# Deep Learning
torch>=2.0.0
torchvision>=0.15.0

# Visualization
matplotlib>=3.7.0
seaborn>=0.12.0
plotly>=5.15.0
dash>=2.11.0

# Database
sqlalchemy>=2.0.0
influxdb-client>=1.36.0

# Configuration
pyyaml>=6.0
python-dotenv>=1.0.0

# Testing
pytest>=7.4.0
pytest-cov>=4.1.0
pytest-mock>=3.11.0

# Monitoring and logging
prometheus-client>=0.17.0
structlog>=23.1.0

# API and web
fastapi>=0.100.0
uvicorn>=0.23.0
requests>=2.31.0

# Utilities
python-dateutil>=2.8.0
pytz>=2023.3
tqdm>=4.65.0

# Development tools
black>=23.7.0
flake8>=6.0.0
mypy>=1.5.0
pre-commit>=3.3.0

# Documentation
sphinx>=7.1.0
sphinx-rtd-theme>=1.3.0

# Scheduling y Parquet
schedule>=1.2.0
pyarrow>=13.0.0 