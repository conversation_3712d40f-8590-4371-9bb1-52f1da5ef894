# Exodus v2025 Trading System Makefile

.PHONY: help install install-dev install-sim install-live test lint format type-check security-check clean build docker-build docker-run setup-dev setup-prod backup restore

# Default target
help:
	@echo "Exodus v2025 Trading System"
	@echo "Available commands:"
	@echo "  install          Install base dependencies"
	@echo "  install-dev      Install development dependencies"
	@echo "  install-sim      Install simulation dependencies"
	@echo "  install-live     Install live trading dependencies"
	@echo "  test             Run all tests"
	@echo "  test-unit        Run unit tests"
	@echo "  test-integration Run integration tests"
	@echo "  lint             Run linting checks"
	@echo "  format           Format code"
	@echo "  type-check       Run type checking"
	@echo "  security-check   Run security checks"
	@echo "  clean            Clean build artifacts"
	@echo "  build            Build distribution packages"
	@echo "  docker-build     Build Docker images"
	@echo "  docker-run       Run Docker containers"
	@echo "  setup-dev        Setup development environment"
	@echo "  setup-prod       Setup production environment"
	@echo "  backup           Backup system data"
	@echo "  restore          Restore from backup"

# Installation targets
install:
	poetry install

install-dev:
	poetry install --with dev

install-sim:
	poetry install --with simulation,dev

install-live:
	poetry install --with live,simulation,monitoring,security,dev

# Testing targets
test: test-unit test-integration

test-unit:
	poetry run pytest tests/unit/ -v --cov=src --cov-report=html --cov-report=term

test-integration:
	poetry run pytest tests/integration/ -v

test-simulation:
	poetry run pytest tests/simulation/ -v

test-live:
	poetry run pytest tests/live/ -v --tb=short

# Code quality targets
lint:
	poetry run flake8 src/ tests/
	poetry run pylint src/
	poetry run bandit -r src/

format:
	poetry run black src/ tests/
	poetry run isort src/ tests/

type-check:
	poetry run mypy src/

security-check:
	poetry run bandit -r src/
	poetry run safety check
	poetry run pip-audit

# Development targets
clean:
	find . -type f -name "*.pyc" -delete
	find . -type d -name "__pycache__" -delete
	find . -type d -name "*.egg-info" -exec rm -rf {} +
	rm -rf build/
	rm -rf dist/
	rm -rf .coverage
	rm -rf htmlcov/
	rm -rf .pytest_cache/
	rm -rf .mypy_cache/

build:
	poetry build

# Docker targets
docker-build:
	docker build -f docker/simulation/Dockerfile -t exodus-sim:latest .
	docker build -f docker/live/Dockerfile -t exodus-live:latest .

docker-run-sim:
	docker-compose -f docker/simulation/docker-compose.yml up -d

docker-run-live:
	docker-compose -f docker/live/docker-compose.yml up -d

docker-stop:
	docker-compose -f docker/simulation/docker-compose.yml down
	docker-compose -f docker/live/docker-compose.yml down

# Environment setup
setup-dev:
	@echo "Setting up development environment..."
	cp config/environments/development.yaml.template config/environments/development.yaml
	cp secrets/local/.env.template .env
	mkdir -p logs/application logs/system logs/security
	mkdir -p data/raw data/processed data/external data/interim
	mkdir -p results/simulations results/live results/analysis
	@echo "Development environment setup complete!"
	@echo "Please edit .env and config/environments/development.yaml with your settings"

setup-prod:
	@echo "Setting up production environment..."
	@echo "Please ensure all secrets are properly configured in Vault"
	@echo "Run: make install-live"
	@echo "Configure: config/environments/production.yaml"

# Database operations
db-migrate:
	poetry run alembic upgrade head

db-reset:
	poetry run alembic downgrade base
	poetry run alembic upgrade head

db-seed:
	poetry run python scripts/database/seed_data.py

# Simulation operations
run-backtest:
	poetry run python scripts/simulation/run_simulation.py --strategy momentum --symbol AAPL

run-optimization:
	poetry run python scripts/simulation/optimize_strategy.py --strategy momentum

generate-report:
	poetry run python scripts/utilities/generate_reports.py --simulation latest

# Live trading operations (use with caution)
start-paper-trading:
	poetry run python scripts/live/start_paper_trading.py

start-live-trading:
	@echo "WARNING: This will start live trading with real money!"
	@read -p "Are you sure? (yes/no): " confirm && [ "$$confirm" = "yes" ]
	poetry run python scripts/live/start_live_trading.py

stop-trading:
	poetry run python scripts/live/stop_trading.py

# Monitoring operations
start-monitoring:
	docker-compose -f docker/monitoring/docker-compose.yml up -d

stop-monitoring:
	docker-compose -f docker/monitoring/docker-compose.yml down

view-logs:
	tail -f logs/application/trading.log

# Maintenance operations
backup:
	poetry run python scripts/maintenance/backup_system.py

restore:
	@read -p "Enter backup ID: " backup_id; \
	poetry run python scripts/maintenance/restore_system.py --backup-id $$backup_id

health-check:
	poetry run python scripts/maintenance/health_check.py

system-diagnostics:
	poetry run python scripts/maintenance/system_diagnostics.py

# Data operations
download-data:
	poetry run python scripts/data/download_market_data.py --symbol AAPL --start 2023-01-01

update-data:
	poetry run python scripts/data/update_market_data.py

validate-data:
	poetry run python scripts/data/validate_data.py

# Deployment operations
deploy-staging:
	./deploy/scripts/deploy.sh --env staging --version latest

deploy-production:
	@echo "WARNING: This will deploy to production!"
	@read -p "Enter version to deploy: " version; \
	@read -p "Confirm deployment to production (yes/no): " confirm && [ "$$confirm" = "yes" ]; \
	./deploy/scripts/deploy.sh --env production --version $$version

rollback:
	@read -p "Enter version to rollback to: " version; \
	./deploy/scripts/rollback.sh --env production --version $$version

# Jupyter notebook
notebook:
	poetry run jupyter lab

# Pre-commit hooks
pre-commit-install:
	poetry run pre-commit install

pre-commit-run:
	poetry run pre-commit run --all-files

# CI/CD helpers
ci-test:
	make lint
	make type-check
	make security-check
	make test

ci-build:
	make clean
	make build
	make docker-build

# Documentation
docs-build:
	poetry run sphinx-build -b html docs/ docs/_build/

docs-serve:
	poetry run python -m http.server 8000 --directory docs/_build/

# Performance testing
perf-test:
	poetry run python scripts/testing/performance_test.py

load-test:
	poetry run python scripts/testing/load_test.py

# Feature flags
enable-feature:
	@read -p "Enter feature name: " feature; \
	poetry run python scripts/feature_flags/enable_feature.py --feature $$feature

disable-feature:
	@read -p "Enter feature name: " feature; \
	poetry run python scripts/feature_flags/disable_feature.py --feature $$feature

# Secrets management
encrypt-secrets:
	poetry run python secrets/scripts/encrypt_secrets.py

decrypt-secrets:
	poetry run python secrets/scripts/decrypt_secrets.py

rotate-keys:
	poetry run python secrets/scripts/rotate_keys.py
