version: '3.8'
services:
  # 1) Tu código de trading/backtest
  exodus-app:
    build:
      context: .
      dockerfile: docker/simulation/Dockerfile
    container_name: exodus-app-dev
    environment:
      # Ajusta para que use solo TimescaleDB
      - EXODUS_ENV=development
      - DATABASE_URL=********************************************************/exodus_timeseries
    volumes:
      - .:/app
    command: tail -f /dev/null  # Mantener vivo para pruebas interactivas
    networks:
      - dev-net

  # 2) Base de datos de series temporales
  timescaledb-dev:
    image: timescale/timescaledb:latest-pg15
    container_name: timescaledb-dev
    environment:
      - POSTGRES_DB=exodus_timeseries
      - POSTGRES_USER=timescale
      - POSTGRES_PASSWORD=timescale123
    ports:
      - "5432:5432"
    volumes:
      - timescale_dev_data:/var/lib/postgresql/data
    networks:
      - dev-net

  # 3) <PERSON><PERSON><PERSON> sin instalar tus deps de producción
  jupyter-dev:
    image: jupyter/datascience-notebook:latest
    container_name: jupyter-dev
    command: start-notebook.sh --NotebookApp.token=''
    ports:
      - "8888:8888"
    volumes:
      - .:/home/<USER>/work
    networks:
      - dev-net

volumes:
  timescale_dev_data:

networks:
  dev-net:
    driver: bridge
