import _plotly_utils.basevalidators


class TicklabelshiftValidator(_plotly_utils.basevalidators.IntegerValidator):
    def __init__(
        self, plotly_name="ticklabelshift", parent_name="layout.yaxis", **kwargs
    ):
        super(TicklabelshiftValidator, self).__init__(
            plotly_name=plotly_name,
            parent_name=parent_name,
            edit_type=kwargs.pop("edit_type", "ticks"),
            **kwargs,
        )
