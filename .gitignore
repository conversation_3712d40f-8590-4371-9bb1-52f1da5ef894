# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
#  Usually these files are written by a python script from a template
#  before PyInstaller builds the exe, so as to inject date/other infos into it.
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
#   According to pypa/pipenv#598, it is recommended to include Pipfile.lock in version control.
#   However, in case of collaboration, if having platform-specific dependencies or dependencies
#   having no cross-platform support, pipenv may install dependencies that don't work, or not
#   install all needed dependencies.
#Pipfile.lock

# PEP 582; used by e.g. github.com/David-OConnor/pyflow
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# Exodus-specific ignores
# Logs
logs/*.log
logs/application/*.log
logs/system/*.log
logs/security/*.log

# Data files
data/raw/*
data/processed/*
data/external/*
data/interim/*
!data/*/README.md
!data/*/.gitkeep

# Results
results/simulations/*
results/live/*
results/analysis/*
!results/*/README.md
!results/*/.gitkeep

# Models
models/mlflow/mlruns/*
models/artifacts/*
models/strategies/*.pkl
models/risk_models/*.pkl
!models/*/README.md
!models/*/.gitkeep

# Secrets and credentials
secrets/local/.env
secrets/local/*.yaml
secrets/local/*.json
secrets/vault/policies/*
secrets/aws_secrets/*
!secrets/local/.env.template
!secrets/*/README.md

# Configuration overrides
config/environments/local.yaml
config/live/broker_configs/*.yaml
config/security/*.yaml
!config/environments/development.yaml
!config/*/README.md

# Temporary files
*.tmp
*.temp
.DS_Store
Thumbs.db

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# Docker
.dockerignore

# Terraform
deploy/environments/*/terraform/.terraform/
deploy/environments/*/terraform/*.tfstate
deploy/environments/*/terraform/*.tfstate.backup
deploy/environments/*/terraform/.terraform.lock.hcl
deploy/infrastructure/*/.terraform/
deploy/infrastructure/*/terraform.tfstate
deploy/infrastructure/*/terraform.tfstate.backup

# Ansible
deploy/automation/ansible/inventory/hosts
deploy/automation/ansible/group_vars/all/vault.yml

# Kubernetes secrets
deploy/environments/*/kubernetes/secrets/

# Monitoring data
monitoring/dashboards/grafana/data/
monitoring/metrics/prometheus/data/

# Backup files
*.bak
*.backup

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
