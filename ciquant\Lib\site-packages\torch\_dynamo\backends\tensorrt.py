# mypy: ignore-errors

# import torch  # type: ignore[import]
# from .common import device_from_inputs, fake_tensor_unsupported  # type: ignore[import]
# from .registry import register_backend  # type: ignore[import]

"""
Placeholder for TensorRT backend for dynamo via torch-tensorrt
"""

# @register_backend
# def tensorrt(gm, example_inputs):
#    import torch_tensorrt # type: ignore[import]
#    pass
