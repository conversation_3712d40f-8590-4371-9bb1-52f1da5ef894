@echo off
echo Starting Exodus Development Environment...
echo.

REM Navigate to project directory
cd /d "%~dp0.."

REM Start Docker services with dev profile
echo Starting Docker services...
docker-compose --profile dev up -d

REM Wait a moment for services to start
timeout /t 5 /nobreak > nul

REM Check if services are running
echo.
echo Checking service status...
docker ps --filter "name=exodus" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"

echo.
echo Development environment is ready!
echo - InfluxDB: http://localhost:8086
echo - TimescaleDB: localhost:5433
echo - Jupyter: http://localhost:8888
echo.
pause 