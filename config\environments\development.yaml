# Development Environment Configuration

environment:
  name: development
  debug: true
  log_level: DEBUG
  
database:
  host: localhost
  port: 5432
  name: exodus_dev
  user: exodus_dev
  password: ${POSTGRES_PASSWORD}
  pool_size: 5
  
redis:
  host: localhost
  port: 6379
  db: 0
  password: ${REDIS_PASSWORD}
  
api:
  host: 0.0.0.0
  port: 8000
  reload: true
  workers: 1
  
logging:
  level: DEBUG
  format: detailed
  file_rotation: daily
  retention_days: 7
  
monitoring:
  enabled: true
  metrics_port: 9090
  health_check_interval: 30
  
simulation:
  default_mode: true
  fast_execution: true
  data_validation: relaxed
  
live_trading:
  enabled: false
  paper_trading: true
  risk_checks: strict
  
security:
  vault_enabled: false
  local_secrets: true
  encryption_enabled: false
  
feature_flags:
  new_strategy_engine: true
  advanced_risk_management: true
  ml_predictions: false
  
data_sources:
  yahoo_finance: true
  alpha_vantage: true
  polygon: false
  iex: false
