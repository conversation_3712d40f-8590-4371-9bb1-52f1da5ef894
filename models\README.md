# Models Directory

## Overview

Model versioning, storage, and management for machine learning models, trading strategies, and risk models used in the Exodus system.

## Structure

### 📁 mlflow/
MLflow experiment tracking and model registry
- **mlruns/**: Experiment runs and metadata
- **artifacts/**: Model artifacts and outputs
- **experiments/**: Experiment configurations

### 📁 dvc/
DVC (Data Version Control) for data and model versioning
- Version controlled datasets
- Model pipeline definitions
- Reproducible model training

### 📁 strategies/
Trading strategy models
- `momentum_v1.pkl`: Momentum strategy model
- `mean_reversion_v2.pkl`: Mean reversion model
- `ml_strategy_v3.pkl`: Machine learning strategy

### 📁 risk_models/
Risk management models
- `var_model_v1.pkl`: Value at Risk model
- `stress_test_v1.pkl`: Stress testing model
- `correlation_model_v2.pkl`: Correlation analysis model

### 📁 feature_stores/
Feature engineering and storage
- **feast/**: Feast feature store configuration
- **custom/**: Custom feature pipelines

### 📁 model_registry/
Model metadata and registry
- `metadata.yaml`: Model metadata
- `lineage.yaml`: Model lineage tracking
- `performance_metrics.yaml`: Model performance history

### 📁 artifacts/
Model artifacts and outputs
- **preprocessing/**: Data preprocessing artifacts
- **trained_models/**: Serialized trained models
- **evaluation_results/**: Model evaluation outputs

## Model Lifecycle

### Development
1. Experiment tracking with MLflow
2. Feature engineering and validation
3. Model training and hyperparameter tuning
4. Performance evaluation and validation

### Staging
1. Model registration in MLflow
2. A/B testing preparation
3. Integration testing
4. Performance monitoring setup

### Production
1. Model deployment
2. Real-time monitoring
3. Performance tracking
4. Automated retraining triggers

## Usage

### MLflow Integration
```python
import mlflow
import mlflow.sklearn

# Log model
with mlflow.start_run():
    mlflow.sklearn.log_model(model, "momentum_strategy")
    mlflow.log_metric("sharpe_ratio", 1.85)
```

### Model Loading
```python
from src.strategies.ml_strategy import MLStrategy

# Load production model
strategy = MLStrategy.load_model("models/strategies/ml_strategy_v3.pkl")
```

### Feature Store
```python
from feast import FeatureStore

# Load features
fs = FeatureStore(repo_path="models/feature_stores/feast")
features = fs.get_online_features(
    features=["price_features:rsi", "price_features:macd"],
    entity_rows=[{"symbol": "AAPL"}]
)
```
