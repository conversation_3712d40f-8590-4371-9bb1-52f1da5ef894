# Live trading dependencies
# These are the core dependencies needed for live trading mode

# Broker APIs
ib-insync==0.9.86
alpaca-trade-api==3.0.0
ccxt==4.0.77
tda-api==1.3.0
robin-stocks==3.0.5

# Real-time data feeds
websocket-client==1.6.1
python-binance==1.0.19
polygon-api-client==1.12.0
iexfinance==0.5.0

# Market data providers
alpha-vantage==2.3.1
twelvedata==1.2.14
finnhub-python==2.4.18

# Real-time processing
asyncio-mqtt==0.13.0
aioredis==2.0.1
aiokafka==0.8.10

# Order management and execution
fix-protocol==0.1.0
quickfix==1.15.1

# Risk management for live trading
numpy-financial==1.0.0
scipy==1.11.1

# Monitoring and alerting
slack-sdk==3.21.3
twilio==8.5.0
sendgrid==6.10.0

# Database connections for live data
psycopg2-binary==2.9.7
pymongo==4.4.1
influxdb-client==1.37.0

# Security and authentication
cryptography==41.0.3
pyjwt==2.8.0
oauth2lib==0.1.0

# Performance monitoring
py-spy==0.3.14
line-profiler==4.0.3
