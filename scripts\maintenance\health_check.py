#!/usr/bin/env python3
"""
Health check script for Exodus v2025
Verifies that the application is running correctly
"""

import sys
import os
import time
import requests
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent.parent / "src"))

def check_application_health():
    """Check if the main application is healthy"""
    try:
        # Try to connect to the main application
        response = requests.get("http://localhost:8000/health", timeout=5)
        return response.status_code == 200
    except Exception:
        return False

def check_database_connection():
    """Check database connectivity"""
    try:
        import psycopg2
        conn = psycopg2.connect(
            host=os.getenv("DB_HOST", "postgres"),
            port=os.getenv("DB_PORT", "5432"),
            database=os.getenv("DB_NAME", "exodus_db"),
            user=os.getenv("DB_USER", "postgres"),
            password=os.getenv("DB_PASSWORD", "exodus123")
        )
        conn.close()
        return True
    except Exception:
        return False

def check_redis_connection():
    """Check Redis connectivity"""
    try:
        import redis
        r = redis.Redis(
            host=os.getenv("REDIS_HOST", "redis"),
            port=int(os.getenv("REDIS_PORT", "6379")),
            password=os.getenv("REDIS_PASSWORD", "redis123"),
            socket_timeout=5
        )
        r.ping()
        return True
    except Exception:
        return False

def main():
    """Main health check function"""
    print("Running Exodus v2025 health check...")
    
    checks = [
        ("Application", check_application_health),
        ("Database", check_database_connection),
        ("Redis", check_redis_connection)
    ]
    
    all_healthy = True
    
    for name, check_func in checks:
        try:
            if check_func():
                print(f"✓ {name}: Healthy")
            else:
                print(f"✗ {name}: Unhealthy")
                all_healthy = False
        except Exception as e:
            print(f"✗ {name}: Error - {e}")
            all_healthy = False
    
    if all_healthy:
        print("All systems healthy!")
        sys.exit(0)
    else:
        print("Some systems are unhealthy!")
        sys.exit(1)

if __name__ == "__main__":
    main()
