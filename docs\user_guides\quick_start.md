# Quick Start Guide

## Welcome to Exodus v2025

This guide will help you get up and running with the Exodus quantitative trading system in minutes.

## Prerequisites

- Python 3.10 or higher
- Git
- 8GB+ RAM recommended
- Internet connection for data feeds

## Installation

### 1. Clone the Repository
```bash
git clone <repository-url>
cd Exodus_v2025
```

### 2. Install Poetry (if not already installed)
```bash
curl -sSL https://install.python-poetry.org | python3 -
```

### 3. Install Dependencies
```bash
# Install base dependencies
poetry install

# For simulation only
poetry install --with simulation

# For live trading (includes simulation)
poetry install --with live,simulation
```

### 4. Environment Setup
```bash
# Copy environment template
cp config/environments/development.yaml.template config/environments/development.yaml

# Edit configuration as needed
nano config/environments/development.yaml
```

## Your First Simulation

### 1. Run a Basic Backtest
```bash
# Run simple momentum strategy backtest
python scripts/simulation/run_simulation.py --strategy momentum --symbol AAPL --start 2023-01-01 --end 2023-12-31
```

### 2. View Results
```bash
# Check results directory
ls results/simulations/

# View performance report
python scripts/utilities/generate_reports.py --simulation latest
```

### 3. Interactive Analysis
```bash
# Start Jupyter notebook
poetry run jupyter notebook notebooks/examples/01_hello_world_backtest.ipynb
```

## Configuration

### Basic Configuration
Edit `config/environments/development.yaml`:

```yaml
# Enable simulation mode
simulation:
  default_mode: true
  initial_cash: 100000
  
# Configure data sources
data_sources:
  yahoo_finance: true
  alpha_vantage: false  # Requires API key
```

### API Keys (Optional)
For enhanced data feeds, add API keys to `.env`:

```bash
# Copy template
cp secrets/local/.env.template .env

# Add your API keys
echo "ALPHA_VANTAGE_API_KEY=your_key_here" >> .env
echo "POLYGON_API_KEY=your_key_here" >> .env
```

## Next Steps

### Learn Strategy Development
```bash
# Open strategy development tutorial
jupyter notebook notebooks/tutorials/strategy_development.ipynb
```

### Explore Examples
- `examples/basic_backtest.py` - Simple backtesting
- `examples/custom_strategy.py` - Custom strategy creation
- `examples/risk_management_example.py` - Risk management setup

### Advanced Features
- [Configuration Guide](configuration_guide.md) - Detailed configuration
- [Strategy Development](strategy_development.md) - Building custom strategies
- [API Reference](../api_reference/) - Complete API documentation

## Common Issues

### Installation Problems
```bash
# Clear poetry cache
poetry cache clear pypi --all

# Reinstall dependencies
poetry install --no-cache
```

### Data Issues
```bash
# Test data connections
python scripts/utilities/test_connections.py

# Download sample data
python scripts/simulation/download_sample_data.py
```

### Performance Issues
```bash
# Check system requirements
python scripts/maintenance/system_diagnostics.py

# Optimize configuration
python scripts/utilities/optimize_config.py
```

## Getting Help

- 📖 [Full Documentation](../README.md)
- 🐛 [Report Issues](https://github.com/your-repo/issues)
- 💬 [Community Discussions](https://github.com/your-repo/discussions)
- 📧 [Email Support](mailto:<EMAIL>)

## What's Next?

1. **Simulation Mastery**: Learn advanced backtesting techniques
2. **Strategy Development**: Build your own trading strategies
3. **Risk Management**: Implement sophisticated risk controls
4. **Live Trading**: Deploy strategies in live markets (paper trading first!)

Welcome to algorithmic trading with Exodus v2025! 🚀
