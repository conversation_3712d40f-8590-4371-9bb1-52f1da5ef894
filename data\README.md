# Data Directory

## Overview

Central data storage and management for the Exodus trading system, organized by data lifecycle and processing stages.

## Structure

### 📁 raw/
Raw, unprocessed data as received from sources
- Market data downloads
- Economic indicators
- News feeds
- Alternative data sources

### 📁 processed/
Cleaned and processed data ready for analysis
- Normalized price data
- Technical indicators
- Feature engineered datasets
- Aggregated market data

### 📁 external/
External data sources and references
- Benchmark data
- Economic calendars
- Corporate actions
- Reference data

### 📁 interim/
Intermediate data during processing pipelines
- Temporary processing files
- Pipeline checkpoints
- Validation datasets

## Data Management

### Data Sources
- Yahoo Finance
- Alpha Vantage
- Polygon.io
- IEX Cloud
- Quandl
- Custom data providers

### Data Types
- OHLCV price data
- Tick-by-tick data
- Options data
- Futures data
- Economic indicators
- News sentiment

### Data Quality
- Automated validation checks
- Gap detection and filling
- Outlier identification
- Data lineage tracking

## Usage

```python
from src.data.storage.timeseries_db import TimeSeriesDB

# Load processed data
db = TimeSeriesDB()
data = db.get_price_data("AAPL", "2023-01-01", "2023-12-31")
```
