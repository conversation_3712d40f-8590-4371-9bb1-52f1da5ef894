# Simulation Module

## Overview

The simulation module provides a comprehensive backtesting and simulation environment for testing trading strategies before deploying them in live markets.

## Workflow and Responsibilities

### 🎯 Purpose
- Provide realistic market simulation for strategy testing
- Validate trading logic before live deployment
- Performance analysis and optimization
- Risk assessment in controlled environment

### 📋 Key Components

#### broker/
Broker simulation components
- `order_simulator.py`: Simulates order execution with realistic fills
- `slippage_model.py`: Models market impact and slippage
- `latency_model.py`: Simulates order latency and delays
- `commission_model.py`: Commission and fee calculations

#### market_data/
Market data simulation
- `data_streamer.py`: Streams historical data as real-time
- `tick_generator.py`: Generates realistic tick data
- `market_hours.py`: Market session and holiday handling

#### environment/
Simulation environment control
- `sim_engine.py`: Main simulation engine
- `time_controller.py`: Time management and fast-forward
- `market_simulator.py`: Overall market state simulation

#### validation/
Simulation validation and analysis
- `performance_tracker.py`: Performance metrics calculation
- `accuracy_checker.py`: Simulation accuracy validation
- `stress_tester.py`: Stress testing scenarios

## Usage

```python
from simulation.environment.sim_engine import SimulationEngine
from simulation.broker.order_simulator import OrderSimulator

# Initialize simulation
sim_engine = SimulationEngine(
    start_date="2023-01-01",
    end_date="2023-12-31",
    initial_cash=100000
)

# Run simulation
results = sim_engine.run_strategy(strategy)
```

## Configuration

Simulation parameters are configured through YAML files in `config/simulation/`:
- `broker_sim.yaml`: Broker simulation settings
- `market_sim.yaml`: Market data settings
- `latency_params.yaml`: Latency modeling parameters
- `slippage_params.yaml`: Slippage modeling parameters
