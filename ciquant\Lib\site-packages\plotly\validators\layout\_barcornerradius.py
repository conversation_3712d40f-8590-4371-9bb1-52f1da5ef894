import _plotly_utils.basevalidators


class BarcornerradiusValidator(_plotly_utils.basevalidators.AnyValidator):
    def __init__(self, plotly_name="barcornerradius", parent_name="layout", **kwargs):
        super(BarcornerradiusValidator, self).__init__(
            plotly_name=plotly_name,
            parent_name=parent_name,
            edit_type=kwargs.pop("edit_type", "calc"),
            **kwargs,
        )
