[tool.poetry]
name = "exodus-v2025"
version = "2025.1.0"
description = "Advanced Quantitative Trading System"
authors = ["Your Name <<EMAIL>>"]
readme = "README.md"
packages = [{include = "src"}]

[tool.poetry.dependencies]
python = "^3.10"
pandas = "^2.0.0"
numpy = "^1.24.0"
scikit-learn = "^1.3.0"
pydantic = "^2.0.0"
fastapi = "^0.100.0"
uvicorn = "^0.23.0"
sqlalchemy = "^2.0.0"
alembic = "^1.11.0"
redis = "^4.6.0"
celery = "^5.3.0"
prometheus-client = "^0.17.0"
structlog = "^23.1.0"
click = "^8.1.0"
pyyaml = "^6.0"
python-dotenv = "^1.0.0"
httpx = "^0.24.0"
websockets = "^11.0"

[tool.poetry.group.simulation.dependencies]
backtrader = "^1.9.76"
zipline = "^1.4.1"
pyfolio = "^0.9.2"
empyrical = "^0.5.5"

[tool.poetry.group.live.dependencies]
ib-insync = "^0.9.86"
alpaca-trade-api = "^3.0.0"
ccxt = "^4.0.0"
tda-api = "^1.3.0"

[tool.poetry.group.distributed.dependencies]
dask = "^2023.0.0"
ray = "^2.0.0"
celery = "^5.3.0"
kubernetes = "^27.0.0"

[tool.poetry.group.feature-flags.dependencies]
launchdarkly-server-sdk = "^8.0.0"
flipper-client = "^0.25.0"

[tool.poetry.group.monitoring.dependencies]
prometheus-client = "^0.17.0"
grafana-api = "^1.0.3"
opentelemetry-api = "^1.19.0"
opentelemetry-sdk = "^1.19.0"
opentelemetry-instrumentation = "^0.40b0"
jaeger-client = "^4.8.0"

[tool.poetry.group.security.dependencies]
hvac = "^1.1.0"
cryptography = "^41.0.0"
keyring = "^24.2.0"

[tool.poetry.group.dev.dependencies]
pytest = "^7.0.0"
pytest-cov = "^4.1.0"
pytest-asyncio = "^0.21.0"
black = "^23.0.0"
flake8 = "^6.0.0"
isort = "^5.12.0"
mypy = "^1.0.0"
pre-commit = "^3.3.0"
bandit = "^1.7.5"

[tool.poetry.group.ml.dependencies]
mlflow = "^2.5.0"
dvc = "^3.0.0"
feast = "^0.32.0"
optuna = "^3.2.0"
shap = "^0.42.0"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.black]
line-length = 88
target-version = ['py310']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
include_trailing_comma = true
force_grid_wrap = 0
use_parentheses = true
ensure_newline_before_comments = true
line_length = 88

[tool.mypy]
python_version = "3.10"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[tool.pytest.ini_options]
minversion = "6.0"
addopts = "-ra -q --strict-markers --strict-config"
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
    "simulation: marks tests as simulation tests",
    "live: marks tests as live trading tests",
]

[tool.coverage.run]
source = ["src"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/conftest.py",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]
