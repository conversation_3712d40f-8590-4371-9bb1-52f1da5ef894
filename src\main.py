#!/usr/bin/env python3
"""
Main entry point for Exodus v2025 Trading System
"""

import asyncio
import logging
import os
import sys
from pathlib import Path

# Add src to Python path
sys.path.insert(0, str(Path(__file__).parent))

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
import uvicorn

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Create FastAPI app
app = FastAPI(
    title="Exodus v2025 Trading System",
    description="Advanced Quantitative Trading System with Crypto Integration",
    version="2025.1.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "Exodus v2025 Trading System",
        "version": "2025.1.0",
        "status": "running"
    }

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "timestamp": "2025-01-05T00:00:00Z",
        "services": {
            "api": "running",
            "database": "connected",
            "redis": "connected"
        }
    }

@app.get("/api/v1/status")
async def api_status():
    """API status endpoint"""
    return {
        "api_version": "v1",
        "trading_engine": "initialized",
        "data_feeds": "connected",
        "risk_management": "active"
    }

async def startup_tasks():
    """Startup tasks for the application"""
    logger.info("Starting Exodus v2025 Trading System...")
    
    # Initialize components here
    logger.info("Initializing trading engine...")
    logger.info("Connecting to data feeds...")
    logger.info("Setting up risk management...")
    
    logger.info("Exodus v2025 startup complete!")

@app.on_event("startup")
async def startup_event():
    """FastAPI startup event"""
    await startup_tasks()

@app.on_event("shutdown")
async def shutdown_event():
    """FastAPI shutdown event"""
    logger.info("Shutting down Exodus v2025 Trading System...")

def main():
    """Main function"""
    port = int(os.getenv("PORT", 8000))
    host = os.getenv("HOST", "0.0.0.0")
    
    logger.info(f"Starting Exodus v2025 on {host}:{port}")
    
    uvicorn.run(
        "main:app",
        host=host,
        port=port,
        reload=False,
        log_level="info"
    )

if __name__ == "__main__":
    main()
