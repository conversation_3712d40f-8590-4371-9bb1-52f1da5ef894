#!/usr/bin/env python3
"""
Script de validación del entorno Exodus v2025
Verifica que todos los componentes necesarios estén instalados y funcionando correctamente.
"""

import os
import sys
import subprocess
import socket
import time
import psycopg2
import redis
from pathlib import Path
from typing import Dict, List, Tuple, Optional

# Colores para output
class Colors:
    GREEN = '\033[92m'
    RED = '\033[91m'
    YELLOW = '\033[93m'
    BLUE = '\033[94m'
    ENDC = '\033[0m'
    BOLD = '\033[1m'

def print_status(message: str, status: str, details: str = ""):
    """Imprime el estado de una verificación con colores"""
    if status == "OK":
        color = Colors.GREEN
        symbol = "✓"
    elif status == "WARNING":
        color = Colors.YELLOW
        symbol = "⚠"
    elif status == "ERROR":
        color = Colors.RED
        symbol = "✗"
    else:
        color = Colors.BLUE
        symbol = "ℹ"
    
    print(f"{color}{symbol} {message}{Colors.ENDC}")
    if details:
        print(f"  {details}")

def check_command_exists(command: str) -> Tuple[bool, str]:
    """Verifica si un comando existe en el sistema"""
    try:
        result = subprocess.run(['which', command], capture_output=True, text=True)
        if result.returncode == 0:
            return True, result.stdout.strip()
        else:
            return False, f"Comando '{command}' no encontrado"
    except Exception as e:
        return False, str(e)

def check_python_version() -> Tuple[bool, str]:
    """Verifica la versión de Python"""
    version = sys.version_info
    if version.major == 3 and version.minor >= 8:
        return True, f"Python {version.major}.{version.minor}.{version.micro}"
    else:
        return False, f"Python {version.major}.{version.minor}.{version.micro} (se requiere 3.8+)"

def check_poetry() -> Tuple[bool, str]:
    """Verifica Poetry y sus dependencias"""
    exists, path = check_command_exists('poetry')
    if not exists:
        return False, "Poetry no está instalado"
    
    try:
        result = subprocess.run(['poetry', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            return True, result.stdout.strip()
        else:
            return False, "Error al ejecutar poetry --version"
    except Exception as e:
        return False, str(e)

def check_docker() -> Tuple[bool, str]:
    """Verifica Docker"""
    exists, path = check_command_exists('docker')
    if not exists:
        return False, "Docker no está instalado"
    
    try:
        result = subprocess.run(['docker', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            return True, result.stdout.strip()
        else:
            return False, "Error al ejecutar docker --version"
    except Exception as e:
        return False, str(e)

def check_docker_compose() -> Tuple[bool, str]:
    """Verifica Docker Compose"""
    exists, path = check_command_exists('docker-compose')
    if not exists:
        return False, "Docker Compose no está instalado"
    
    try:
        result = subprocess.run(['docker-compose', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            return True, result.stdout.strip()
        else:
            return False, "Error al ejecutar docker-compose --version"
    except Exception as e:
        return False, str(e)

def check_port_available(host: str, port: int) -> Tuple[bool, str]:
    """Verifica si un puerto está disponible"""
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as sock:
            sock.settimeout(5)
            result = sock.connect_ex((host, port))
            if result == 0:
                return True, f"Puerto {port} está en uso (servicio activo)"
            else:
                return False, f"Puerto {port} está disponible"
    except Exception as e:
        return False, str(e)

def check_database_connection(host: str, port: int, database: str, user: str, password: str) -> Tuple[bool, str]:
    """Verifica la conexión a PostgreSQL"""
    try:
        conn = psycopg2.connect(
            host=host,
            port=port,
            database=database,
            user=user,
            password=password,
            connect_timeout=10
        )
        conn.close()
        return True, f"Conexión exitosa a {database}"
    except psycopg2.OperationalError as e:
        return False, f"Error de conexión: {str(e)}"
    except Exception as e:
        return False, f"Error inesperado: {str(e)}"

def check_redis_connection(host: str, port: int, password: str = None) -> Tuple[bool, str]:
    """Verifica la conexión a Redis"""
    try:
        r = redis.Redis(host=host, port=port, password=password, socket_timeout=10)
        r.ping()
        return True, f"Conexión exitosa a Redis"
    except redis.ConnectionError as e:
        return False, f"Error de conexión: {str(e)}"
    except Exception as e:
        return False, f"Error inesperado: {str(e)}"

def check_docker_services() -> Dict[str, Tuple[bool, str]]:
    """Verifica el estado de los servicios Docker"""
    services = {}
    
    try:
        result = subprocess.run(['docker-compose', 'ps'], capture_output=True, text=True)
        if result.returncode == 0:
            lines = result.stdout.strip().split('\n')
            for line in lines[2:]:  # Skip header lines
                if line.strip():
                    parts = line.split()
                    if len(parts) >= 2:
                        service_name = parts[0]
                        status = ' '.join(parts[1:])
                        services[service_name] = (True, status)
        else:
            services['docker-compose'] = (False, "Error al ejecutar docker-compose ps")
    except Exception as e:
        services['docker-compose'] = (False, str(e))
    
    return services

def check_environment_files() -> Dict[str, Tuple[bool, str]]:
    """Verifica la existencia de archivos de configuración"""
    files_to_check = [
        '.env',
        'docker-compose.yml',
        'pyproject.toml',
        'scripts/database/init_postgres.sql',
        'scripts/database/init_timescaledb.sql',
        'scripts/maintenance/health_check.py',
        'src/main.py'
    ]
    
    results = {}
    for file_path in files_to_check:
        path = Path(file_path)
        if path.exists():
            results[file_path] = (True, f"Archivo existe ({path.stat().st_size} bytes)")
        else:
            results[file_path] = (False, "Archivo no encontrado")
    
    return results

def load_env_variables() -> Dict[str, str]:
    """Carga las variables de entorno desde .env"""
    env_vars = {}
    env_file = Path('.env')
    
    if env_file.exists():
        with open(env_file, 'r') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    env_vars[key.strip()] = value.strip()
    
    return env_vars

def main():
    """Función principal de validación"""
    print(f"{Colors.BOLD}{Colors.BLUE}=== Validación del Entorno Exodus v2025 ==={Colors.ENDC}\n")
    
    # Verificar Python
    print(f"{Colors.BOLD}1. Verificando Python...{Colors.ENDC}")
    success, details = check_python_version()
    print_status("Versión de Python", "OK" if success else "ERROR", details)
    
    # Verificar Poetry
    print(f"\n{Colors.BOLD}2. Verificando Poetry...{Colors.ENDC}")
    success, details = check_poetry()
    print_status("Poetry", "OK" if success else "ERROR", details)
    
    # Verificar Docker
    print(f"\n{Colors.BOLD}3. Verificando Docker...{Colors.ENDC}")
    success, details = check_docker()
    print_status("Docker", "OK" if success else "ERROR", details)
    
    # Verificar Docker Compose
    print(f"\n{Colors.BOLD}4. Verificando Docker Compose...{Colors.ENDC}")
    success, details = check_docker_compose()
    print_status("Docker Compose", "OK" if success else "ERROR", details)
    
    # Verificar archivos de configuración
    print(f"\n{Colors.BOLD}5. Verificando archivos de configuración...{Colors.ENDC}")
    file_results = check_environment_files()
    for file_path, (success, details) in file_results.items():
        print_status(f"Archivo {file_path}", "OK" if success else "ERROR", details)
    
    # Cargar variables de entorno
    env_vars = load_env_variables()
    
    # Verificar puertos
    print(f"\n{Colors.BOLD}6. Verificando puertos...{Colors.ENDC}")
    ports_to_check = [
        ('PostgreSQL', 'localhost', 5432),
        ('TimescaleDB', 'localhost', 5433),
        ('Redis', 'localhost', 6379),
        ('FastAPI', 'localhost', 8000),
        ('Prometheus', 'localhost', 9090),
        ('Grafana', 'localhost', 3000)
    ]
    
    for service, host, port in ports_to_check:
        success, details = check_port_available(host, port)
        status = "OK" if success else "WARNING"
        print_status(f"Puerto {port} ({service})", status, details)
    
    # Verificar servicios Docker
    print(f"\n{Colors.BOLD}7. Verificando servicios Docker...{Colors.ENDC}")
    docker_services = check_docker_services()
    for service, (success, details) in docker_services.items():
        print_status(f"Servicio {service}", "OK" if success else "ERROR", details)
    
    # Verificar conexiones a bases de datos (si están disponibles)
    print(f"\n{Colors.BOLD}8. Verificando conexiones a bases de datos...{Colors.ENDC}")
    
    # PostgreSQL
    if env_vars.get('POSTGRES_HOST'):
        success, details = check_database_connection(
            env_vars.get('POSTGRES_HOST', 'localhost'),
            int(env_vars.get('POSTGRES_PORT', 5432)),
            env_vars.get('POSTGRES_DB', 'exodus_trading'),
            env_vars.get('POSTGRES_USER', 'exodus_user'),
            env_vars.get('POSTGRES_PASSWORD', '')
        )
        print_status("PostgreSQL", "OK" if success else "WARNING", details)
    
    # TimescaleDB
    if env_vars.get('TIMESCALEDB_HOST'):
        success, details = check_database_connection(
            env_vars.get('TIMESCALEDB_HOST', 'localhost'),
            int(env_vars.get('TIMESCALEDB_PORT', 5433)),
            env_vars.get('TIMESCALEDB_DB', 'exodus_timeseries'),
            env_vars.get('TIMESCALEDB_USER', 'timescale_user'),
            env_vars.get('TIMESCALEDB_PASSWORD', '')
        )
        print_status("TimescaleDB", "OK" if success else "WARNING", details)
    
    # Redis
    if env_vars.get('REDIS_HOST'):
        success, details = check_redis_connection(
            env_vars.get('REDIS_HOST', 'localhost'),
            int(env_vars.get('REDIS_PORT', 6379)),
            env_vars.get('REDIS_PASSWORD')
        )
        print_status("Redis", "OK" if success else "WARNING", details)
    
    print(f"\n{Colors.BOLD}{Colors.GREEN}=== Validación completada ==={Colors.ENDC}")
    print(f"{Colors.YELLOW}Nota: Los servicios marcados como WARNING pueden estar detenidos intencionalmente.{Colors.ENDC}")
    print(f"{Colors.YELLOW}Ejecuta 'docker-compose up -d' para iniciar los servicios.{Colors.ENDC}")

if __name__ == "__main__":
    main()
