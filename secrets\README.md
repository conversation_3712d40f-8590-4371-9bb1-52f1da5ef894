# Secrets Management

## Overview

Secure management of API keys, credentials, and sensitive configuration data for the Exodus trading system.

## Security Principles

### 🔐 Never Commit Secrets
- No API keys or passwords in version control
- Use environment variables and secure vaults
- Encrypt sensitive local files

### 🛡️ Principle of Least Privilege
- Role-based access to secrets
- Time-limited access tokens
- Audit all secret access

### 🔄 Regular Rotation
- Automated key rotation
- Expiration monitoring
- Backup key management

## Structure

### 📁 vault/
HashiCorp Vault integration
- **policies/**: Vault access policies
- **auth/**: Authentication methods
- **kv/**: Key-value secret storage

### 📁 aws_secrets/
AWS Secrets Manager integration
- **staging/**: Staging environment secrets
- **production/**: Production environment secrets

### 📁 local/
Local development secrets
- `.env.template`: Environment variable template
- `api_keys.yaml.enc`: Encrypted API keys for development

### 📁 scripts/
Secret management utilities
- `encrypt_secrets.py`: Encrypt sensitive files
- `decrypt_secrets.py`: Decrypt for local use
- `rotate_keys.py`: Automated key rotation

## Secret Categories

### Broker API Keys
- Interactive Brokers credentials
- Alpaca API keys
- Binance API secrets
- TD Ameritrade tokens

### Data Provider Keys
- Alpha Vantage API key
- Polygon.io API key
- IEX Cloud token
- Quandl API key

### Infrastructure Secrets
- Database passwords
- Redis authentication
- Cloud provider credentials
- Monitoring service tokens

### Notification Secrets
- Slack webhook URLs
- Email service credentials
- SMS service tokens
- PagerDuty integration keys

## Usage

### Development Environment
```bash
# Copy template
cp secrets/local/.env.template .env

# Edit with your keys
nano .env

# Encrypt for storage
python secrets/scripts/encrypt_secrets.py .env
```

### Production Environment
```python
import hvac

# Connect to Vault
client = hvac.Client(url='https://vault.company.com')
client.token = os.environ['VAULT_TOKEN']

# Read secret
secret = client.secrets.kv.v2.read_secret_version(path='exodus/prod/ib-api')
api_key = secret['data']['data']['api_key']
```

### Environment Variables
```bash
# Required environment variables
export VAULT_ADDR="https://vault.company.com"
export VAULT_TOKEN="your-vault-token"
export EXODUS_ENV="production"
```

## Security Best Practices

### Local Development
- Use encrypted files for API keys
- Never commit `.env` files
- Use separate development API keys
- Limit development key permissions

### Production
- Use HashiCorp Vault or cloud secret managers
- Implement secret rotation
- Monitor secret access
- Use service accounts with minimal permissions

### CI/CD
- Store secrets in CI/CD secret stores
- Use temporary tokens when possible
- Audit all secret usage
- Implement secret scanning

## Emergency Procedures

### Compromised Keys
1. Immediately revoke compromised keys
2. Generate new keys
3. Update all systems
4. Audit access logs
5. Document incident

### Vault Outage
1. Use emergency backup keys
2. Implement manual key distribution
3. Monitor system access
4. Restore vault service
5. Verify key integrity
