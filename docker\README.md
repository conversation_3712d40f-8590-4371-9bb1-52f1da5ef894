# Docker Configuration

## Overview

Containerization setup for the Exodus trading system, providing consistent environments across development, staging, and production.

## Structure

### 📁 simulation/
Simulation environment containers
- `Dockerfile`: Simulation-specific container
- `docker-compose.yml`: Simulation stack
- `requirements.txt`: Simulation dependencies

### 📁 live/
Live trading environment containers
- `Dockerfile`: Production trading container
- `docker-compose.yml`: Live trading stack
- `requirements.txt`: Live trading dependencies

### 📁 monitoring/
Monitoring stack containers
- **grafana/**: Grafana dashboard container
- **prometheus/**: Prometheus metrics container
- **jaeger/**: Distributed tracing container

### 📁 databases/
Database containers
- **timescaledb/**: Time-series database
- **redis/**: Caching and message queue

### 📁 orchestration/
Container orchestration
- **kubernetes/**: Kubernetes manifests
- **helm/**: Helm charts for deployment

## Quick Start

### Development Environment
```bash
# Start development stack
docker-compose -f docker/simulation/docker-compose.yml up -d

# View logs
docker-compose -f docker/simulation/docker-compose.yml logs -f

# Stop stack
docker-compose -f docker/simulation/docker-compose.yml down
```

### Production Environment
```bash
# Build production images
docker build -f docker/live/Dockerfile -t exodus-live:latest .

# Start production stack
docker-compose -f docker/live/docker-compose.yml up -d
```

## Container Images

### Base Image
```dockerfile
FROM python:3.10-slim

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    make \
    && rm -rf /var/lib/apt/lists/*

# Install Poetry
RUN pip install poetry

# Set working directory
WORKDIR /app

# Copy dependency files
COPY pyproject.toml poetry.lock ./

# Install dependencies
RUN poetry config virtualenvs.create false \
    && poetry install --no-dev
```

### Simulation Container
```dockerfile
FROM exodus-base:latest

# Install simulation dependencies
RUN poetry install --with simulation

# Copy application code
COPY src/ ./src/
COPY simulation/ ./simulation/
COPY config/ ./config/

# Set entrypoint
ENTRYPOINT ["python", "scripts/simulation/run_simulation.py"]
```

### Live Trading Container
```dockerfile
FROM exodus-base:latest

# Install live trading dependencies
RUN poetry install --with live,monitoring,security

# Copy application code
COPY src/ ./src/
COPY live/ ./live/
COPY config/ ./config/

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD python scripts/live/health_check.py

# Set entrypoint
ENTRYPOINT ["python", "scripts/live/start_live_trading.py"]
```

## Docker Compose Stacks

### Simulation Stack
```yaml
version: '3.8'

services:
  simulation:
    build:
      context: ../..
      dockerfile: docker/simulation/Dockerfile
    environment:
      - EXODUS_ENV=development
      - DATABASE_URL=**************************************/exodus
    depends_on:
      - db
      - redis
    volumes:
      - ../../results:/app/results
      - ../../logs:/app/logs

  db:
    image: timescale/timescaledb:latest-pg15
    environment:
      - POSTGRES_DB=exodus
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    volumes:
      - db_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"

volumes:
  db_data:
```

### Live Trading Stack
```yaml
version: '3.8'

services:
  trading-engine:
    build:
      context: ../..
      dockerfile: docker/live/Dockerfile
    environment:
      - EXODUS_ENV=production
      - DATABASE_URL=postgresql://postgres:${POSTGRES_PASSWORD}@db:5432/exodus
      - REDIS_URL=redis://redis:6379/0
    depends_on:
      - db
      - redis
      - monitoring
    volumes:
      - ../../logs:/app/logs
      - ../../secrets:/app/secrets:ro
    restart: unless-stopped

  risk-monitor:
    build:
      context: ../..
      dockerfile: docker/live/Dockerfile
    command: ["python", "scripts/live/risk_monitor.py"]
    environment:
      - EXODUS_ENV=production
    depends_on:
      - trading-engine
    restart: unless-stopped

  db:
    image: timescale/timescaledb:latest-pg15
    environment:
      - POSTGRES_DB=exodus
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
    volumes:
      - db_data:/var/lib/postgresql/data
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    command: redis-server --requirepass ${REDIS_PASSWORD}
    restart: unless-stopped

  monitoring:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus/config:/etc/prometheus
    restart: unless-stopped

volumes:
  db_data:
```

## Kubernetes Deployment

### Namespace
```yaml
apiVersion: v1
kind: Namespace
metadata:
  name: exodus-trading
```

### Deployment
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: trading-engine
  namespace: exodus-trading
spec:
  replicas: 2
  selector:
    matchLabels:
      app: trading-engine
  template:
    metadata:
      labels:
        app: trading-engine
    spec:
      containers:
      - name: trading-engine
        image: exodus-live:latest
        env:
        - name: EXODUS_ENV
          value: "production"
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: database-secret
              key: url
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
```

### Service
```yaml
apiVersion: v1
kind: Service
metadata:
  name: trading-engine-service
  namespace: exodus-trading
spec:
  selector:
    app: trading-engine
  ports:
  - port: 8080
    targetPort: 8080
  type: ClusterIP
```

## Security Considerations

### Image Security
- Use minimal base images
- Regular security updates
- Vulnerability scanning
- Non-root user execution

### Runtime Security
- Read-only root filesystem
- Resource limits
- Network policies
- Secret management

### Production Hardening
```dockerfile
# Create non-root user
RUN adduser --disabled-password --gecos '' appuser

# Set file permissions
RUN chown -R appuser:appuser /app
USER appuser

# Read-only filesystem
VOLUME ["/tmp"]
```

## Monitoring and Logging

### Container Metrics
- CPU and memory usage
- Network I/O
- Disk usage
- Container health

### Application Metrics
- Trading performance
- Error rates
- Response times
- Business metrics

### Log Aggregation
```yaml
logging:
  driver: "json-file"
  options:
    max-size: "10m"
    max-file: "3"
```

## Troubleshooting

### Common Issues
```bash
# Check container logs
docker logs <container_id>

# Execute into container
docker exec -it <container_id> /bin/bash

# Check resource usage
docker stats

# Inspect container
docker inspect <container_id>
```

### Performance Optimization
- Multi-stage builds
- Layer caching
- Resource limits
- Health checks
