# Exodus v2025 - Configuración de Entorno
# Copiar este archivo a .env y ajustar los valores según sea necesario

# =============================================================================
# CONFIGURACIÓN DE BASE DE DATOS
# =============================================================================

# PostgreSQL Principal
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_DB=exodus_trading
POSTGRES_USER=exodus_user
POSTGRES_PASSWORD=exodus_secure_password_2025

# TimescaleDB (Series Temporales)
TIMESCALEDB_HOST=localhost
TIMESCALEDB_PORT=5433
TIMESCALEDB_DB=exodus_timeseries
TIMESCALEDB_USER=timescale_user
TIMESCALEDB_PASSWORD=timescale_secure_password_2025

# Redis (Cache y Sesiones)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=redis_secure_password_2025
REDIS_DB=0

# =============================================================================
# CONFIGURACIÓN DE APLICACIÓN
# =============================================================================

# Entorno de ejecución
ENVIRONMENT=development
DEBUG=true
LOG_LEVEL=INFO

# Configuración de la aplicación
APP_NAME=Exodus_v2025
APP_VERSION=2025.1.0
APP_HOST=0.0.0.0
APP_PORT=8000

# Clave secreta para JWT y encriptación
SECRET_KEY=your_super_secret_key_change_this_in_production_2025
JWT_SECRET_KEY=your_jwt_secret_key_change_this_in_production_2025
JWT_ALGORITHM=HS256
JWT_EXPIRATION_HOURS=24

# =============================================================================
# CONFIGURACIÓN DE APIS EXTERNAS
# =============================================================================

# Binance API
BINANCE_API_KEY=your_binance_api_key_here
BINANCE_SECRET_KEY=your_binance_secret_key_here
BINANCE_TESTNET=true

# CoinGecko API
COINGECKO_API_KEY=your_coingecko_api_key_here
COINGECKO_BASE_URL=https://api.coingecko.com/api/v3

# Alpha Vantage (opcional para datos adicionales)
ALPHA_VANTAGE_API_KEY=your_alpha_vantage_api_key_here

# =============================================================================
# CONFIGURACIÓN DE TRADING
# =============================================================================

# Configuración general de trading
DEFAULT_EXCHANGE=binance
MAX_CONCURRENT_ORDERS=100
DEFAULT_TIMEFRAME=1h
RISK_MANAGEMENT_ENABLED=true
MAX_POSITION_SIZE_PCT=10.0

# Configuración de simulación
SIMULATION_INITIAL_BALANCE=10000.0
SIMULATION_COMMISSION_RATE=0.001

# =============================================================================
# CONFIGURACIÓN DE MACHINE LEARNING
# =============================================================================

# MLflow
MLFLOW_TRACKING_URI=http://localhost:5000
MLFLOW_EXPERIMENT_NAME=crypto_price_prediction
MLFLOW_ARTIFACT_ROOT=./mlruns

# Configuración de modelos
MODEL_RETRAIN_INTERVAL_HOURS=24
MODEL_PREDICTION_INTERVAL_MINUTES=15
FEATURE_ENGINEERING_LOOKBACK_PERIODS=100

# =============================================================================
# CONFIGURACIÓN DE MONITOREO
# =============================================================================

# Prometheus
PROMETHEUS_HOST=localhost
PROMETHEUS_PORT=9090
PROMETHEUS_METRICS_PATH=/metrics

# Grafana
GRAFANA_HOST=localhost
GRAFANA_PORT=3000
GRAFANA_ADMIN_USER=admin
GRAFANA_ADMIN_PASSWORD=admin_password_2025

# Configuración de alertas
ALERT_EMAIL_ENABLED=false
ALERT_EMAIL_SMTP_HOST=smtp.gmail.com
ALERT_EMAIL_SMTP_PORT=587
ALERT_EMAIL_USERNAME=<EMAIL>
ALERT_EMAIL_PASSWORD=your_email_password
ALERT_EMAIL_FROM=<EMAIL>
ALERT_EMAIL_TO=<EMAIL>

# Slack (opcional)
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK
SLACK_CHANNEL=#trading-alerts

# =============================================================================
# CONFIGURACIÓN DE SEGURIDAD
# =============================================================================

# CORS
CORS_ORIGINS=http://localhost:3000,http://localhost:8080
CORS_ALLOW_CREDENTIALS=true

# Rate Limiting
RATE_LIMIT_REQUESTS_PER_MINUTE=60
RATE_LIMIT_BURST=10

# Encriptación de datos sensibles
DATA_ENCRYPTION_KEY=your_32_character_encryption_key_here

# =============================================================================
# CONFIGURACIÓN DE DESARROLLO
# =============================================================================

# Hot reload para desarrollo
RELOAD=true
WORKERS=1

# Configuración de testing
TEST_DATABASE_URL=postgresql://test_user:test_password@localhost:5432/exodus_test
TEST_REDIS_URL=redis://localhost:6379/1

# =============================================================================
# CONFIGURACIÓN DE PRODUCCIÓN
# =============================================================================

# Configuración para producción (comentado por defecto)
# ENVIRONMENT=production
# DEBUG=false
# WORKERS=4
# RELOAD=false
# LOG_LEVEL=WARNING

# SSL/TLS
# SSL_CERT_PATH=/path/to/ssl/cert.pem
# SSL_KEY_PATH=/path/to/ssl/key.pem

# Base de datos en producción
# POSTGRES_HOST=your_production_db_host
# POSTGRES_PORT=5432
# POSTGRES_SSL_MODE=require

# =============================================================================
# CONFIGURACIÓN DE DOCKER
# =============================================================================

# Configuración específica para Docker
DOCKER_POSTGRES_DATA_PATH=./data/postgres
DOCKER_TIMESCALEDB_DATA_PATH=./data/timescaledb
DOCKER_REDIS_DATA_PATH=./data/redis
DOCKER_GRAFANA_DATA_PATH=./data/grafana
DOCKER_PROMETHEUS_DATA_PATH=./data/prometheus

# Network
DOCKER_NETWORK_NAME=exodus-network

# =============================================================================
# CONFIGURACIÓN DE BACKUP
# =============================================================================

# Configuración de respaldos
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *  # Diario a las 2 AM
BACKUP_RETENTION_DAYS=30
BACKUP_S3_BUCKET=your-backup-bucket
BACKUP_S3_REGION=us-east-1
BACKUP_S3_ACCESS_KEY=your_s3_access_key
BACKUP_S3_SECRET_KEY=your_s3_secret_key
