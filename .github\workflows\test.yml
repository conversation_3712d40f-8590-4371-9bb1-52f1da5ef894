name: Tests

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ["3.10", "3.11"]

    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: exodus_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
    - uses: actions/checkout@v4

    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}

    - name: Install Poetry
      uses: snok/install-poetry@v1
      with:
        version: latest
        virtualenvs-create: true
        virtualenvs-in-project: true

    - name: Load cached venv
      id: cached-poetry-dependencies
      uses: actions/cache@v3
      with:
        path: .venv
        key: venv-${{ runner.os }}-${{ matrix.python-version }}-${{ hashFiles('**/poetry.lock') }}

    - name: Install dependencies
      if: steps.cached-poetry-dependencies.outputs.cache-hit != 'true'
      run: |
        poetry install --with dev,simulation,live,distributed,feature-flags,monitoring,security,ml

    - name: Run linting
      run: |
        poetry run black --check .
        poetry run flake8 .
        poetry run isort --check-only .
        poetry run mypy src/

    - name: Run security checks
      run: |
        poetry run bandit -r src/

    - name: Run unit tests
      run: |
        poetry run pytest tests/ -v --cov=src --cov-report=xml --cov-report=term-missing -m "not integration and not performance"
      env:
        DATABASE_URL: postgresql://postgres:postgres@localhost:5432/exodus_test
        REDIS_URL: redis://localhost:6379/0

    - name: Run integration tests
      run: |
        poetry run pytest tests/integration/ -v -m integration
      env:
        DATABASE_URL: postgresql://postgres:postgres@localhost:5432/exodus_test
        REDIS_URL: redis://localhost:6379/0

    - name: Test failsafe systems
      run: |
        poetry run pytest tests/disaster_recovery/ -v

    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml
        flags: unittests
        name: codecov-umbrella
