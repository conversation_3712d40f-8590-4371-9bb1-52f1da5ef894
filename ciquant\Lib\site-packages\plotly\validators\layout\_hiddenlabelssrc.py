import _plotly_utils.basevalidators


class HiddenlabelssrcValidator(_plotly_utils.basevalidators.SrcValidator):
    def __init__(self, plotly_name="hiddenlabelssrc", parent_name="layout", **kwargs):
        super(HiddenlabelssrcValidator, self).__init__(
            plotly_name=plotly_name,
            parent_name=parent_name,
            edit_type=kwargs.pop("edit_type", "none"),
            **kwargs,
        )
