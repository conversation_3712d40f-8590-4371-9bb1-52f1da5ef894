# Source Code Architecture

## Overview

The `src/` directory contains the core trading system components that provide a unified interface for both simulation and live trading environments. The architecture follows clean architecture principles with clear separation of concerns.

## Module Structure

### 🎯 execution/
Main trading engine and execution components
- **interfaces/**: Abstract interfaces for broker, data, and order management
- **adapters/**: Concrete implementations for different brokers and environments
- **engine/**: Core trading engine and signal processing
- **order_management/**: Order routing, tracking, and fill management

### 🛡️ risk_management/
Risk management and monitoring
- Real-time risk monitoring
- Position sizing algorithms
- Circuit breakers and risk limits
- Risk calculation utilities

### 📊 portfolio/
Portfolio management and tracking
- Portfolio state management
- Position tracking and management
- Cash management
- Performance tracking and analytics

### 📈 data/
Data management and processing
- **providers/**: Data source integrations (Yahoo, Alpha Vantage, etc.)
- **storage/**: Database and caching layers
- **processing/**: Data pipelines and transformations
- **validation/**: Data quality and anomaly detection
- **feeds/**: Real-time and historical data feeds

### 🎯 strategies/
Trading strategy implementations
- Base strategy framework
- Momentum strategies
- Mean reversion strategies
- Machine learning strategies

### 🔧 utils/
Shared utilities and helpers
- Logging configuration
- Validation utilities
- Decorators and common functions
- Custom exceptions

## Design Principles

1. **Adapter Pattern**: Clean separation between trading logic and broker-specific implementations
2. **Dependency Injection**: Configurable components for easy testing and swapping
3. **Event-Driven Architecture**: Asynchronous processing for real-time operations
4. **Type Safety**: Full type hints and validation using Pydantic
5. **Testability**: All components designed for easy unit and integration testing

## Usage Examples

```python
from src import TradingEngine, PortfolioManager, RiskMonitor

# Initialize core components
engine = TradingEngine(config=config)
portfolio = PortfolioManager(initial_cash=100000)
risk_monitor = RiskMonitor(limits=risk_limits)

# Start trading
engine.start()
```
