from typing import Any, Dict, List, Optional, Union

import numpy as np

from ..typing import Array<PERSON>ike
from . import Array
from .request import Request
from ..config import PluginConfig

def _get_config(plugin: str) -> PluginConfig: ...

class Format(object):
    @property
    def doc(self) -> str: ...
    @property
    def name(self) -> str: ...
    @property
    def description(self) -> str: ...
    @property
    def extensions(self) -> List[str]: ...
    @property
    def modes(self) -> str: ...
    def __init__(
        self,
        name: str,
        description: str,
        extensions: Union[str, list, tuple, None] = None,
        modes: str = None,
    ) -> None: ...
    def __repr__(self) -> str: ...
    def __str__(self) -> str: ...
    def get_reader(self, request: Request) -> Reader: ...
    def get_writer(self, request: Request) -> Writer: ...
    def can_read(self, request: Request) -> bool: ...
    def can_write(self, request: Request) -> bool: ...
    def _can_read(self, request: Request) -> bool: ...
    def _can_write(self, request: Request) -> bool: ...

    class _BaseReaderWriter(object):
        @property
        def format(self) -> Format: ...
        @property
        def request(self) -> Request: ...
        @property
        def closed(self) -> bool: ...
        def __init__(self, format: Format, request: Request) -> None: ...
        def __enter__(self) -> Format._BaseReaderWriter: ...
        def __exit__(self, type, value, traceback) -> None: ...
        def __del__(self) -> None: ...
        def close(self) -> None: ...
        def _checkClosed(self, msg=None) -> None: ...
        def _open(self, **kwargs) -> None: ...
        def _close(self) -> None: ...

    class Reader(_BaseReaderWriter):
        def get_length(self) -> int: ...
        def get_data(self, index: int, **kwargs) -> Array: ...
        def get_next_data(self, **kwargs) -> Dict[str, Any]: ...
        def set_image_index(self, index: int, **kwargs) -> None: ...
        def get_meta_data(self, index: int = None) -> Dict[str, Any]: ...
        def iter_data(self) -> Array: ...
        def __iter__(self) -> Array: ...
        def __len__(self) -> int: ...
        def _get_length(self) -> int: ...
        def _get_data(self, index: int) -> Array: ...
        def _get_meta_data(self, index: int) -> Dict[str, Any]: ...

    class Writer(_BaseReaderWriter):
        def append_data(self, im: ArrayLike, meta: Dict[str, Any] = None) -> None: ...
        def set_meta_data(self, meta: Dict[str, Any]) -> None: ...
        def _append_data(self, im: ArrayLike, meta: Dict[str, Any]) -> None: ...
        def _set_meta_data(self, meta: Dict[str, Any]) -> None: ...

class FormatManager(object):
    @property
    def _formats(self) -> List[Format]: ...
    def __repr__(self) -> str: ...
    def __iter__(self) -> Format: ...
    def __len__(self) -> int: ...
    def __str__(self) -> str: ...
    def __getitem__(self, name: str) -> Format: ...
    def sort(self, *names: str) -> None: ...
    def add_format(self, iio_format: Format, overwrite: bool = False) -> None: ...
    def search_read_format(self, request: Request) -> Optional[Format]: ...
    def search_write_format(self, request: Request) -> Optional[Format]: ...
    def get_format_names(self) -> List[str]: ...
    def show(self) -> None: ...
