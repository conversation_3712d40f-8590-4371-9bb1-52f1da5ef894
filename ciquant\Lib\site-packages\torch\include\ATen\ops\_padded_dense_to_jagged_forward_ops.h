#pragma once

// @generated by torchgen/gen.py from Operator.h

#include <string_view>
#include <tuple>
#include <vector>

// Forward declarations of any types needed in the operator signatures.
// We can't directly include these classes because it will cause circular include dependencies.
// This file is included by TensorBody.h, which defines the Tensor class.
#include <ATen/core/ATen_fwd.h>

namespace at {
namespace _ops {


struct TORCH_API _padded_dense_to_jagged_forward {
  using schema = at::Tensor (const at::Tensor &, at::TensorList, ::std::optional<c10::SymInt>);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::_padded_dense_to_jagged_forward";
  static constexpr const char* overload_name = "";
  static constexpr const char* schema_str = "_padded_dense_to_jagged_forward(Tensor dense, Tensor[] offsets, SymInt? total_L=None) -> Tensor";
  static at::Tensor call(const at::Tensor & dense, at::TensorList offsets, ::std::optional<c10::SymInt> total_L);
  static at::Tensor redispatch(c10::DispatchKeySet dispatchKeySet, const at::Tensor & dense, at::TensorList offsets, ::std::optional<c10::SymInt> total_L);
};

}} // namespace at::_ops
