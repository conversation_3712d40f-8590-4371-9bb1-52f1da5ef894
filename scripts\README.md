# Scripts Guide

## Overview

Collection of automation scripts for simulation, live trading, deployment, maintenance, and utilities.

## Script Categories

### 📁 simulation/
Simulation execution and analysis scripts
- `run_simulation.py`: Execute trading simulations
- `validate_simulation.py`: Validate simulation results
- `analyze_simulation.py`: Analyze simulation performance
- `batch_simulation.py`: Run multiple simulations in parallel

### 📁 live/
Live trading control scripts
- `start_live_trading.py`: Start live trading system
- `stop_live_trading.py`: Gracefully stop live trading
- `emergency_stop.py`: Emergency trading halt
- `health_check.py`: System health verification
- `warm_up.py`: System warm-up and preparation

### 📁 deployment/
Deployment and release scripts
- `deploy_simulation.py`: Deploy simulation environment
- `deploy_live.py`: Deploy live trading environment
- `rollback.py`: Rollback to previous version
- `blue_green_deploy.py`: Blue-green deployment strategy
- `canary_deploy.py`: Canary deployment strategy

### 📁 maintenance/
System maintenance scripts
- `backup_data.py`: Backup trading data and configurations
- `cleanup_logs.py`: Log rotation and cleanup
- `update_models.py`: Update ML models and strategies
- `database_maintenance.py`: Database optimization and maintenance
- `system_diagnostics.py`: Comprehensive system diagnostics

### 📁 utilities/
General utility scripts
- `migrate_config.py`: Configuration migration utilities
- `test_connections.py`: Test broker and data connections
- `generate_reports.py`: Generate trading reports
- `encrypt_decrypt.py`: Encryption/decryption utilities
- `performance_profiler.py`: System performance profiling

## Usage Examples

### Running Simulations
```bash
# Basic simulation
python scripts/simulation/run_simulation.py

# Simulation with custom config
python scripts/simulation/run_simulation.py --config config/simulation/custom.yaml

# Batch simulations
python scripts/simulation/batch_simulation.py --strategies momentum,mean_reversion
```

### Live Trading
```bash
# Pre-flight checks
python scripts/live/health_check.py

# Start live trading
python scripts/live/start_live_trading.py --config config/live/production.yaml

# Emergency stop
python scripts/live/emergency_stop.py
```

### Deployment
```bash
# Deploy to staging
python scripts/deployment/deploy_simulation.py --env staging

# Blue-green deployment to production
python scripts/deployment/blue_green_deploy.py --env production
```

### Maintenance
```bash
# Daily maintenance
python scripts/maintenance/backup_data.py
python scripts/maintenance/cleanup_logs.py

# System diagnostics
python scripts/maintenance/system_diagnostics.py --full-check
```

## Script Configuration

### Environment Variables
- `EXODUS_ENV`: Target environment (development/staging/production)
- `EXODUS_CONFIG_PATH`: Configuration directory path
- `EXODUS_LOG_LEVEL`: Logging level for scripts

### Command Line Arguments
Most scripts support common arguments:
- `--config`: Configuration file path
- `--env`: Environment name
- `--dry-run`: Preview mode without execution
- `--verbose`: Verbose output
- `--help`: Display help information

## Safety Features

### Pre-flight Checks
All critical scripts include pre-flight checks:
- Configuration validation
- Dependency verification
- Resource availability
- Permission checks

### Confirmation Prompts
Destructive operations require confirmation:
- Live trading start/stop
- Data deletion
- Production deployments

### Logging and Audit
All script executions are logged:
- Execution timestamps
- User identification
- Parameters used
- Results and errors

## Automation Integration

### Cron Jobs
```bash
# Daily backup
0 2 * * * /path/to/scripts/maintenance/backup_data.py

# Log cleanup
0 3 * * * /path/to/scripts/maintenance/cleanup_logs.py
```

### CI/CD Integration
Scripts are integrated with CI/CD pipelines:
- Automated testing
- Deployment automation
- Health monitoring
- Performance tracking
